"use client";

import React, { useState, useEffect } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import {
  createTopicExplanationAttempt,
  addTopicExplanationQuestions,
  submitInitialTopicAnswer,
  addInitialAnalysisToTopicExplanation,
  submitFollowUpAnswer,
  addFollowUpAnalysisToTopicExplanation,
  getTopicExplanationQuestions,
  TopicExplanationQuestion
} from "@/Firebase/firestore/services/RepairCenterExamService";
import { useAuth } from "@/context";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import {
  ArrowLeft,
  ArrowRight,
  Clock,
  MessageSquare,
  Brain,
  CheckCircle,
  XCircle,
  Loader2,
  Target,
  BookOpen
} from "lucide-react";

interface TopicExplanationModeProps {
  certificate: Certificate;
  repairCenterQuestions: Array<{
    questionId: string;
    question: any;
    priority: string;
    successRate: number;
    topics: string[];
  }>;
  onBack: () => void;
}

export default function TopicExplanationMode({
  certificate,
  repairCenterQuestions,
  onBack
}: TopicExplanationModeProps) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<'A' | 'B' | 'C' | 'D' | null>(null);
  const [userExplanation, setUserExplanation] = useState("");
  const [followUpAnswer, setFollowUpAnswer] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [attemptId, setAttemptId] = useState<string | null>(null);
  const [topicQuestions, setTopicQuestions] = useState<TopicExplanationQuestion[]>([]);
  const [currentPhase, setCurrentPhase] = useState<'answer' | 'follow_up' | 'completed'>('answer');
  const [currentAnalysis, setCurrentAnalysis] = useState<any>(null);
  const [startTime] = useState(Date.now());
  const [questionStartTime, setQuestionStartTime] = useState(Date.now());

  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    initializeTopicQuestions();
  }, []);

  const initializeTopicQuestions = async () => {
    if (!user?.uid || !certificate.id) return;

    try {
      // Create topic explanation attempt with repair center questions
      const topics = [...new Set(repairCenterQuestions.flatMap(q => q.topics))];
      const attemptId = await createTopicExplanationAttempt(
        user.uid,
        certificate.id,
        topics
      );
      setAttemptId(attemptId);

      // Convert repair center questions to topic explanation questions
      const topicQuestionData = repairCenterQuestions.map((repairQ, index) => ({
        originalQuestionId: repairQ.questionId,
        originalQuestion: {
          question: repairQ.question.question,
          choiceA: repairQ.question.choiceA,
          choiceB: repairQ.question.choiceB,
          choiceC: repairQ.question.choiceC,
          choiceD: repairQ.question.choiceD,
          correctAnswer: repairQ.question.correctAnswer,
          category: repairQ.question.category
        },
        status: 'initial' as const
      }));

      await addTopicExplanationQuestions(attemptId, topicQuestionData);

      // Fetch the created questions with IDs
      const createdQuestions = await getTopicExplanationQuestions(attemptId);
      setTopicQuestions(createdQuestions);

    } catch (error) {
      console.error('Error initializing topic questions:', error);
      toast({
        title: "Error",
        description: "Failed to initialize topic explanation mode.",
        variant: "destructive",
      });
    }
  };

  const handleSubmitInitialAnswer = async () => {
    if (!attemptId || !selectedAnswer || !userExplanation.trim() || !topicQuestions[currentQuestionIndex]?.id) return;

    try {
      setIsSubmitting(true);
      const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);
      const currentQuestion = topicQuestions[currentQuestionIndex];

      // Submit the initial answer and explanation
      await submitInitialTopicAnswer(
        attemptId,
        currentQuestion.id!,
        selectedAnswer,
        userExplanation,
        timeSpent
      );

      // Analyze with AI
      setIsAnalyzing(true);
      const response = await fetch('/api/AnalyzeDeepUnderstanding', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          question: currentQuestion.originalQuestion.question,
          choiceA: currentQuestion.originalQuestion.choiceA,
          choiceB: currentQuestion.originalQuestion.choiceB,
          choiceC: currentQuestion.originalQuestion.choiceC,
          choiceD: currentQuestion.originalQuestion.choiceD,
          correctAnswer: currentQuestion.originalQuestion.correctAnswer,
          userAnswer: selectedAnswer,
          userExplanation,
          certificateName: certificate.name,
          isFollowUp: false
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // Store AI analysis
          await addInitialAnalysisToTopicExplanation(
            attemptId,
            currentQuestion.id!,
            {
              understandingScore: result.data.understandingScore,
              isAnswerCorrect: result.data.isAnswerCorrect,
              mainTopicsAssessed: result.data.mainTopicsAssessed,
              feedback: result.data.feedback,
              knowledgeGaps: result.data.knowledgeGaps,
              shouldProceedToNext: result.data.shouldProceedToNext
            },
            result.data.followUpQuestion
          );

          setCurrentAnalysis(result.data);

          // Check if we need a follow-up question
          if (result.data.followUpQuestion && result.data.understandingScore >= 5) {
            setCurrentPhase('follow_up');
            toast({
              title: "Good! Now let's test deeper understanding",
              description: `Score: ${result.data.understandingScore}/10 - Answer the follow-up question.`,
            });
          } else if (result.data.shouldProceedToNext && result.data.understandingScore >= 5) {
            setCurrentPhase('completed');
            toast({
              title: "Excellent understanding!",
              description: `Score: ${result.data.understandingScore}/10 - Moving to next question.`,
            });
          } else {
            setCurrentPhase('completed');
            toast({
              title: "Review needed",
              description: `Score: ${result.data.understandingScore}/10 - Study the feedback before continuing.`,
              variant: "destructive",
            });
          }
        }
      }

    } catch (error) {
      console.error('Error submitting initial answer:', error);
      toast({
        title: "Error",
        description: "Failed to submit answer.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
      setIsAnalyzing(false);
    }
  };

  const handleSubmitFollowUp = async () => {
    if (!attemptId || !followUpAnswer.trim() || !topicQuestions[currentQuestionIndex]?.id) return;

    try {
      setIsSubmitting(true);
      const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);
      const currentQuestion = topicQuestions[currentQuestionIndex];

      // Submit the follow-up answer
      await submitFollowUpAnswer(
        attemptId,
        currentQuestion.id!,
        followUpAnswer,
        timeSpent
      );

      // Analyze follow-up with AI
      setIsAnalyzing(true);
      const response = await fetch('/api/AnalyzeDeepUnderstanding', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userExplanation: followUpAnswer,
          isFollowUp: true,
          followUpType: currentAnalysis?.followUpQuestion?.type,
          originalQuestion: currentQuestion.originalQuestion,
          certificateName: certificate.name
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // Store follow-up analysis
          await addFollowUpAnalysisToTopicExplanation(
            attemptId,
            currentQuestion.id!,
            {
              followUpScore: result.data.followUpScore,
              overallUnderstanding: result.data.overallUnderstanding,
              feedback: result.data.feedback,
              conceptsMastered: result.data.conceptsMastered,
              areasForImprovement: result.data.areasForImprovement,
              mainTopicsAssessed: result.data.mainTopicsAssessed,
              readyForNextQuestion: result.data.readyForNextQuestion,
              studyRecommendations: result.data.studyRecommendations
            }
          );

          setCurrentAnalysis(prev => ({ ...prev, followUpAnalysis: result.data }));
          setCurrentPhase('completed');

          toast({
            title: result.data.readyForNextQuestion ? "Great understanding!" : "Keep studying",
            description: `Overall Score: ${result.data.overallUnderstanding}/10`,
            variant: result.data.readyForNextQuestion ? "default" : "destructive",
          });
        }
      }

    } catch (error) {
      console.error('Error submitting follow-up answer:', error);
      toast({
        title: "Error",
        description: "Failed to submit follow-up answer.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
      setIsAnalyzing(false);
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < topicQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setSelectedAnswer(null);
      setUserExplanation("");
      setFollowUpAnswer("");
      setCurrentPhase('answer');
      setCurrentAnalysis(null);
      setQuestionStartTime(Date.now());
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
      setSelectedAnswer(null);
      setUserExplanation("");
      setFollowUpAnswer("");
      setCurrentPhase('answer');
      setCurrentAnalysis(null);
      setQuestionStartTime(Date.now());
    }
  };

  const currentQuestion = topicQuestions[currentQuestionIndex];
  const completedQuestions = topicQuestions.filter(q => q.status === 'completed').length;

  if (!currentQuestion) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading topic questions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Button variant="ghost" onClick={onBack} className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Repair Center
          </Button>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="flex items-center gap-1">
              <Brain className="h-3 w-3" />
              Question {currentQuestionIndex + 1} of {topicQuestions.length}
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {Math.floor((Date.now() - startTime) / 60000)}m
            </Badge>
            <Badge variant="outline">
              Phase: {currentPhase === 'answer' ? 'Answer' : currentPhase === 'follow_up' ? 'Follow-up' : 'Complete'}
            </Badge>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
            <span>Progress</span>
            <span>{completedQuestions} / {topicQuestions.length} completed</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-purple-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(completedQuestions / topicQuestions.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Main GDPR Topics */}
        {currentAnalysis?.mainTopicsAssessed && (
          <div className="mb-6">
            <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200">
              <CardContent className="p-4">
                <h3 className="text-sm font-semibold text-blue-900 dark:text-blue-100 mb-2">
                  Main GDPR Topics Being Assessed:
                </h3>
                <div className="flex flex-wrap gap-2">
                  {currentAnalysis.mainTopicsAssessed.map((topic: string, index: number) => (
                    <Badge key={index} variant="outline" className="text-xs bg-blue-100 text-blue-800 border-blue-300">
                      {topic}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Current Question */}
        {currentPhase === 'answer' && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-purple-600" />
                Answer the Question You Got Wrong
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 dark:text-gray-300 mb-6 text-lg">
                {currentQuestion.originalQuestion.question}
              </p>

              <RadioGroup value={selectedAnswer || ""} onValueChange={(value) => setSelectedAnswer(value as 'A' | 'B' | 'C' | 'D')}>
                <div className="space-y-3 mb-6">
                  {(['A', 'B', 'C', 'D'] as const).map((choice) => (
                    <div key={choice} className="flex items-center space-x-2 p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800">
                      <RadioGroupItem value={choice} id={choice} />
                      <Label htmlFor={choice} className="flex-1 cursor-pointer">
                        <span className="font-medium mr-2">{choice})</span>
                        {currentQuestion.originalQuestion[`choice${choice}` as keyof typeof currentQuestion.originalQuestion] as string}
                      </Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>

              <div className="mb-4">
                <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                  Explain why you chose this answer:
                </Label>
                <Textarea
                  value={userExplanation}
                  onChange={(e) => setUserExplanation(e.target.value)}
                  placeholder="Explain your reasoning for choosing this answer. Be specific about the concepts and principles involved."
                  className="min-h-[120px]"
                />
              </div>

              <Button
                onClick={handleSubmitInitialAnswer}
                disabled={!selectedAnswer || !userExplanation.trim() || isSubmitting || isAnalyzing}
                className="w-full"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : isAnalyzing ? (
                  <>
                    <Brain className="h-4 w-4 mr-2" />
                    Analyzing...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Submit Answer & Explanation
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Follow-up Question */}
        {currentPhase === 'follow_up' && currentAnalysis?.followUpQuestion && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-orange-600" />
                Follow-up Question - Test Your Understanding
              </CardTitle>
              {currentAnalysis.followUpQuestion.minimizedTopicHint && (
                <Badge variant="outline" className="text-xs">
                  Hint: {currentAnalysis.followUpQuestion.minimizedTopicHint}
                </Badge>
              )}
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 dark:text-gray-300 mb-4 text-lg">
                {currentAnalysis.followUpQuestion.question}
              </p>

              <div className="mb-4">
                <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                  Your answer:
                </Label>
                <Textarea
                  value={followUpAnswer}
                  onChange={(e) => setFollowUpAnswer(e.target.value)}
                  placeholder="Provide a detailed explanation demonstrating your understanding..."
                  className="min-h-[150px]"
                />
              </div>

              <Button
                onClick={handleSubmitFollowUp}
                disabled={!followUpAnswer.trim() || isSubmitting || isAnalyzing}
                className="w-full"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : isAnalyzing ? (
                  <>
                    <Brain className="h-4 w-4 mr-2" />
                    Analyzing...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Submit Follow-up Answer
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        )}

        {/* AI Analysis Results */}
        {currentAnalysis && currentPhase !== 'answer' && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-blue-600" />
                AI Analysis - Understanding Score: {currentAnalysis.understandingScore || currentAnalysis.followUpAnalysis?.overallUnderstanding}/10
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Initial Analysis */}
              {currentAnalysis.feedback && (
                <div className={`p-3 rounded-lg ${
                  currentAnalysis.isAnswerCorrect
                    ? 'bg-green-50 border border-green-200 dark:bg-green-900/20'
                    : 'bg-red-50 border border-red-200 dark:bg-red-900/20'
                }`}>
                  <div className="flex items-center gap-2 mb-2">
                    {currentAnalysis.isAnswerCorrect ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600" />
                    )}
                    <span className="font-medium">
                      {currentAnalysis.isAnswerCorrect ? 'Correct Answer' : 'Incorrect Answer'}
                    </span>
                  </div>
                  <p className="text-sm">{currentAnalysis.feedback}</p>
                </div>
              )}

              {/* Knowledge Gaps */}
              {currentAnalysis.knowledgeGaps?.length > 0 && (
                <div>
                  <h4 className="font-semibold text-orange-700 dark:text-orange-400 mb-2">Knowledge Gaps:</h4>
                  <ul className="text-sm space-y-1">
                    {currentAnalysis.knowledgeGaps.map((gap: string, index: number) => (
                      <li key={index} className="flex items-start gap-2">
                        <AlertTriangle className="h-3 w-3 mt-1 text-orange-600 flex-shrink-0" />
                        {gap}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Follow-up Analysis */}
              {currentAnalysis.followUpAnalysis && (
                <div className="border-t pt-4">
                  <h4 className="font-semibold text-purple-700 dark:text-purple-400 mb-2">
                    Follow-up Analysis (Score: {currentAnalysis.followUpAnalysis.followUpScore}/10):
                  </h4>
                  <p className="text-sm text-gray-700 dark:text-gray-300 mb-3">
                    {currentAnalysis.followUpAnalysis.feedback}
                  </p>

                  {currentAnalysis.followUpAnalysis.conceptsMastered?.length > 0 && (
                    <div className="mb-3">
                      <h5 className="font-medium text-green-700 dark:text-green-400 mb-1">Concepts Mastered:</h5>
                      <ul className="text-sm space-y-1">
                        {currentAnalysis.followUpAnalysis.conceptsMastered.map((concept: string, index: number) => (
                          <li key={index} className="flex items-start gap-2">
                            <CheckCircle className="h-3 w-3 mt-1 text-green-600 flex-shrink-0" />
                            {concept}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {currentAnalysis.followUpAnalysis.areasForImprovement?.length > 0 && (
                    <div className="mb-3">
                      <h5 className="font-medium text-orange-700 dark:text-orange-400 mb-1">Areas for Improvement:</h5>
                      <ul className="text-sm space-y-1">
                        {currentAnalysis.followUpAnalysis.areasForImprovement.map((area: string, index: number) => (
                          <li key={index} className="flex items-start gap-2">
                            <Target className="h-3 w-3 mt-1 text-orange-600 flex-shrink-0" />
                            {area}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {currentAnalysis.followUpAnalysis.studyRecommendations?.length > 0 && (
                    <div>
                      <h5 className="font-medium text-blue-700 dark:text-blue-400 mb-1">Study Recommendations:</h5>
                      <ul className="text-sm space-y-1">
                        {currentAnalysis.followUpAnalysis.studyRecommendations.map((rec: string, index: number) => (
                          <li key={index} className="flex items-start gap-2">
                            <BookOpen className="h-3 w-3 mt-1 text-blue-600 flex-shrink-0" />
                            {rec}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Navigation */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePreviousQuestion}
            disabled={currentQuestionIndex === 0}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous Question
          </Button>

          {currentQuestionIndex < topicQuestions.length - 1 ? (
            <Button
              onClick={handleNextQuestion}
              disabled={currentPhase !== 'completed' || (currentAnalysis && currentAnalysis.understandingScore < 5 && (!currentAnalysis.followUpAnalysis || currentAnalysis.followUpAnalysis.overallUnderstanding < 5))}
              className={currentPhase === 'completed' && ((currentAnalysis?.understandingScore >= 5) || (currentAnalysis?.followUpAnalysis?.overallUnderstanding >= 5)) ? 'bg-green-600 hover:bg-green-700' : ''}
            >
              Next Question
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button
              onClick={onBack}
              disabled={currentPhase !== 'completed'}
              className="bg-green-600 hover:bg-green-700"
            >
              Complete Session
              <CheckCircle className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>

        {/* Session Summary */}
        {currentQuestionIndex === topicQuestions.length - 1 && currentPhase === 'completed' && (
          <Card className="mt-6 bg-green-50 dark:bg-green-900/20 border-green-200">
            <CardContent className="p-6 text-center">
              <h3 className="text-lg font-bold text-green-900 dark:text-green-100 mb-2">
                Deep Understanding Session Complete! 🎉
              </h3>
              <div className="text-sm text-green-700 dark:text-green-300">
                <p>Questions Completed: {completedQuestions} / {topicQuestions.length}</p>
                <p>Session Time: {Math.floor((Date.now() - startTime) / 60000)} minutes</p>
                <p className="mt-2 text-xs">All progress and AI feedback has been saved for review.</p>
              </div>
              <div className="mt-4">
                <Button onClick={onBack}>
                  Back to Repair Center
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
