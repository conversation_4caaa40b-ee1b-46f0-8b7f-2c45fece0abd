import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      question,
      userAnswer,
      certificateName
    } = body;

    if (!question || !userAnswer) {
      return NextResponse.json(
        { success: false, error: 'Question and user answer are required' },
        { status: 400 }
      );
    }

    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro" });

    const prompt = `
You are an expert ${certificateName} instructor. A student just answered this question CORRECTLY:

QUESTION: ${question.questionText}
A) ${question.choiceA}
B) ${question.choiceB}
C) ${question.choiceC}
D) ${question.choiceD}

CORRECT ANSWER: ${question.correctAnswer}
STUDENT'S ANSWER: ${userAnswer}
TOPIC: ${question.topic}

Since they got it right, I want to test their DEEP UNDERSTANDING to ensure they truly comprehend the concept and aren't just memorizing. Generate exactly 2 focused follow-up questions that will reveal whether they really understand:

1. WHY their correct answer is right (test conceptual understanding)
2. WHY one specific wrong answer is incorrect (test misconception awareness)

Each follow-up question should:
- Test deep understanding, not memorization
- Be concise and focused
- Avoid repetition
- Be specific to this exact question and topic
- Only provide subtle hints, not detailed explanations

Return as JSON:
{
  "followUpQuestions": [
    {
      "id": "why_correct",
      "type": "why_correct",
      "questionText": "Explain WHY answer ${question.correctAnswer} is correct for this question.",
      "hints": [
        "Think about the key principle",
        "Consider the specific requirements"
      ],
      "relatedChoice": "${question.correctAnswer}"
    },
    {
      "id": "why_incorrect",
      "type": "why_incorrect",
      "questionText": "Choose one wrong answer and explain WHY it's incorrect.",
      "hints": [
        "Identify the misconception",
        "Explain what makes it wrong"
      ]
    }
  ]
}

Make the questions specific to the actual question content and topic. Avoid generic questions.`;

    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();

    try {
      // Try to parse the JSON response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const followUpData = JSON.parse(jsonMatch[0]);
      
      return NextResponse.json({
        success: true,
        data: followUpData
      });

    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      
      // Fallback response if JSON parsing fails
      return NextResponse.json({
        success: true,
        data: {
          followUpQuestions: [
            {
              id: "why_correct",
              type: "why_correct",
              questionText: `Explain WHY answer ${question.correctAnswer} is correct for this question.`,
              hints: [
                "Think about the key principle",
                "Consider the specific requirements"
              ],
              relatedChoice: question.correctAnswer
            },
            {
              id: "why_incorrect",
              type: "why_incorrect",
              questionText: "Choose one wrong answer and explain WHY it's incorrect.",
              hints: [
                "Identify the misconception",
                "Explain what makes it wrong"
              ]
            }
          ]
        }
      });
    }

  } catch (error) {
    console.error('Error in GenerateFollowUpQuestions API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate follow-up questions',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
