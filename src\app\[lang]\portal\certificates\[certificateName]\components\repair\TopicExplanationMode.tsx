"use client";

import React, { useState, useEffect } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import {
  createTopicExplanationAttempt,
  addTopicExplanationQuestions,
  submitTopicExplanationAnswer,
  addAIAnalysisToTopicExplanation,
  getTopicExplanationQuestions,
  TopicExplanationQuestion
} from "@/Firebase/firestore/services/RepairCenterExamService";
import { useAuth } from "@/context";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  ArrowLeft,
  ArrowRight,
  Clock,
  MessageSquare,
  Brain,
  CheckCircle,
  XCircle,
  Loader2,
  Target,
  BookOpen
} from "lucide-react";

interface TopicExplanationModeProps {
  certificate: Certificate;
  topics: string[];
  repairCenterQuestions: Array<{
    questionId: string;
    question: any;
    priority: string;
    successRate: number;
    topics: string[];
  }>;
  onBack: () => void;
}

interface RepairQuestion {
  questionId: string;
  questionText: string;
  choiceA: string;
  choiceB: string;
  choiceC: string;
  choiceD: string;
  correctAnswer: 'A' | 'B' | 'C' | 'D';
  topic: string;
  priority: string;
  successRate: number;
  explanation?: string;
}

interface FollowUpQuestion {
  id: string;
  type: 'why_correct' | 'why_incorrect' | 'concept_explanation' | 'application';
  questionText: string;
  relatedChoice?: 'A' | 'B' | 'C' | 'D';
  expectedPoints: string[];
}

export default function TopicExplanationMode({
  certificate,
  topics,
  repairCenterQuestions,
  onBack
}: TopicExplanationModeProps) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<'A' | 'B' | 'C' | 'D' | null>(null);
  const [userExplanation, setUserExplanation] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [attemptId, setAttemptId] = useState<string | null>(null);
  const [repairQuestions, setRepairQuestions] = useState<RepairQuestion[]>([]);
  const [currentPhase, setCurrentPhase] = useState<'answer' | 'follow_up'>('answer');
  const [followUpQuestions, setFollowUpQuestions] = useState<FollowUpQuestion[]>([]);
  const [currentFollowUpIndex, setCurrentFollowUpIndex] = useState(0);
  const [questionAnswered, setQuestionAnswered] = useState(false);
  const [answerCorrect, setAnswerCorrect] = useState(false);
  const [submittedQuestions, setSubmittedQuestions] = useState<Set<number>>(new Set());
  const [aiAnalyses, setAiAnalyses] = useState<Map<number, any>>(new Map());
  const [startTime] = useState(Date.now());
  const [questionStartTime, setQuestionStartTime] = useState(Date.now());

  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    initializeRepairQuestions();
  }, []);

  const initializeRepairQuestions = async () => {
    if (!user?.uid || !certificate.id || !repairCenterQuestions.length) return;

    try {
      // Create topic explanation attempt
      const attemptId = await createTopicExplanationAttempt(
        user.uid,
        certificate.id,
        topics
      );
      setAttemptId(attemptId);

      // Convert repair center questions to our format
      const questions: RepairQuestion[] = repairCenterQuestions.map(repairQ => ({
        questionId: repairQ.questionId,
        questionText: repairQ.question.question,
        choiceA: repairQ.question.choiceA,
        choiceB: repairQ.question.choiceB,
        choiceC: repairQ.question.choiceC,
        choiceD: repairQ.question.choiceD,
        correctAnswer: repairQ.question.correctAnswer,
        topic: repairQ.question.category || 'General',
        priority: repairQ.priority,
        successRate: repairQ.successRate,
        explanation: repairQ.question.explanation
      }));

      setRepairQuestions(questions);

    } catch (error) {
      console.error('Error initializing repair questions:', error);
      toast({
        title: "Error",
        description: "Failed to initialize topic explanation mode.",
        variant: "destructive",
      });
    }
  };

  const handleAnswerSubmit = async () => {
    if (!selectedAnswer || !repairQuestions[currentQuestionIndex]) return;

    try {
      setIsSubmitting(true);
      const currentQuestion = repairQuestions[currentQuestionIndex];
      const isCorrect = selectedAnswer === currentQuestion.correctAnswer;

      setAnswerCorrect(isCorrect);
      setQuestionAnswered(true);

      if (isCorrect) {
        // Generate follow-up questions to test understanding
        await generateFollowUpQuestions(currentQuestion, selectedAnswer);
        setCurrentPhase('follow_up');
        setCurrentFollowUpIndex(0);
      } else {
        // If wrong, just show explanation and move to next
        toast({
          title: "Incorrect Answer",
          description: `The correct answer was ${currentQuestion.correctAnswer}. Review the explanation.`,
          variant: "destructive",
        });
      }

    } catch (error) {
      console.error('Error submitting answer:', error);
      toast({
        title: "Error",
        description: "Failed to submit answer.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const generateFollowUpQuestions = async (question: RepairQuestion, userAnswer: 'A' | 'B' | 'C' | 'D') => {
    try {
      const response = await fetch('/api/GenerateFollowUpQuestions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          question: {
            questionText: question.questionText,
            choiceA: question.choiceA,
            choiceB: question.choiceB,
            choiceC: question.choiceC,
            choiceD: question.choiceD,
            correctAnswer: question.correctAnswer,
            topic: question.topic,
            explanation: question.explanation
          },
          userAnswer,
          certificateName: certificate.name
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data.followUpQuestions) {
          setFollowUpQuestions(result.data.followUpQuestions);
        }
      }
    } catch (error) {
      console.error('Error generating follow-up questions:', error);
      // Fallback to default follow-up questions
      setFollowUpQuestions([
        {
          id: 'why_correct',
          type: 'why_correct',
          questionText: `Explain why answer ${question.correctAnswer} is correct for this question.`,
          expectedPoints: ['Key concept explanation', 'Supporting reasoning', 'Practical application']
        },
        {
          id: 'why_incorrect',
          type: 'why_incorrect',
          questionText: `Explain why the other answer choices are incorrect.`,
          expectedPoints: ['Identify misconceptions', 'Explain why each is wrong', 'Common pitfalls']
        }
      ]);
    }
  };

  const handleFollowUpSubmit = async () => {
    if (!userExplanation.trim() || !followUpQuestions[currentFollowUpIndex]) return;

    try {
      setIsSubmitting(true);
      setIsAnalyzing(true);

      const currentFollowUp = followUpQuestions[currentFollowUpIndex];
      const currentQuestion = repairQuestions[currentQuestionIndex];

      // Analyze the follow-up explanation
      const response = await fetch('/api/AnalyzeFollowUpExplanation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          originalQuestion: currentQuestion,
          followUpQuestion: currentFollowUp,
          userExplanation,
          certificateName: certificate.name
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setAiAnalyses(prev => new Map(prev.set(`${currentQuestionIndex}_${currentFollowUpIndex}`, result.data)));

          toast({
            title: "Understanding Analyzed",
            description: `Score: ${result.data.score}/10 - ${result.data.feedback.substring(0, 100)}...`,
          });

          // Move to next follow-up question or next main question
          if (currentFollowUpIndex < followUpQuestions.length - 1) {
            setCurrentFollowUpIndex(currentFollowUpIndex + 1);
            setUserExplanation("");
          } else {
            // Completed all follow-ups, mark as done
            setSubmittedQuestions(prev => new Set(prev.add(currentQuestionIndex)));
          }
        }
      }

    } catch (error) {
      console.error('Error submitting follow-up explanation:', error);
      toast({
        title: "Error",
        description: "Failed to analyze explanation.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
      setIsAnalyzing(false);
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < repairQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      resetQuestionState();
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
      resetQuestionState();
    }
  };

  const resetQuestionState = () => {
    setSelectedAnswer(null);
    setUserExplanation("");
    setCurrentPhase('answer');
    setQuestionAnswered(false);
    setAnswerCorrect(false);
    setFollowUpQuestions([]);
    setCurrentFollowUpIndex(0);
    setQuestionStartTime(Date.now());
  };

  const currentQuestion = repairQuestions[currentQuestionIndex];
  const currentFollowUp = followUpQuestions[currentFollowUpIndex];
  const currentAnalysis = aiAnalyses.get(currentPhase === 'answer' ? currentQuestionIndex : `${currentQuestionIndex}_${currentFollowUpIndex}`);
  const isCurrentSubmitted = submittedQuestions.has(currentQuestionIndex);

  if (!currentQuestion) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading repair center questions...</p>
        </div>
      </div>
    );
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'super-critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'critical': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'moderate': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Button variant="ghost" onClick={onBack} className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Repair Center
          </Button>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="flex items-center gap-1">
              <Brain className="h-3 w-3" />
              Question {currentQuestionIndex + 1} of {repairQuestions.length}
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {Math.floor((Date.now() - startTime) / 60000)}m
            </Badge>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
            <span>Progress</span>
            <span>{submittedQuestions.size} / {repairQuestions.length} completed</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-purple-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(submittedQuestions.size / repairQuestions.length) * 100}%` }}
            />
          </div>

          {/* Phase Indicator */}
          <div className="flex items-center justify-center mt-3 gap-4">
            <Badge variant={currentPhase === 'answer' ? 'default' : 'outline'} className="flex items-center gap-1">
              <Target className="h-3 w-3" />
              Phase 1: Answer Question
            </Badge>
            {questionAnswered && answerCorrect && (
              <Badge variant={currentPhase === 'follow_up' ? 'default' : 'outline'} className="flex items-center gap-1">
                <Brain className="h-3 w-3" />
                Phase 2: Explain Understanding ({currentFollowUpIndex + 1}/{followUpQuestions.length})
              </Badge>
            )}
          </div>
        </div>

        {/* Phase 1: Answer the Repair Center Question */}
        {currentPhase === 'answer' && (
          <Card className="mb-6">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5 text-purple-600" />
                  Re-answer this question you got wrong
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Badge className={getPriorityColor(currentQuestion.priority)}>
                    {currentQuestion.priority} priority
                  </Badge>
                  <Badge variant="outline">
                    {currentQuestion.successRate.toFixed(1)}% success rate
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 dark:text-gray-300 mb-6 text-lg">
                {currentQuestion.questionText}
              </p>

              {!questionAnswered && (
                <div className="space-y-3 mb-6">
                  {(['A', 'B', 'C', 'D'] as const).map((choice) => (
                    <Button
                      key={choice}
                      variant={selectedAnswer === choice ? "default" : "outline"}
                      onClick={() => setSelectedAnswer(choice)}
                      className="w-full p-4 h-auto text-left justify-start"
                    >
                      <span className="font-medium mr-3">{choice})</span>
                      {currentQuestion[`choice${choice}` as keyof RepairQuestion] as string}
                    </Button>
                  ))}
                </div>
              )}

              {questionAnswered && (
                <div className="space-y-3 mb-6">
                  {(['A', 'B', 'C', 'D'] as const).map((choice) => {
                    const isCorrect = choice === currentQuestion.correctAnswer;
                    const isUserChoice = choice === selectedAnswer;

                    return (
                      <div
                        key={choice}
                        className={`flex items-center space-x-2 p-4 rounded-lg border ${
                          isCorrect
                            ? 'bg-green-50 border-green-200 dark:bg-green-900/20'
                            : isUserChoice && !isCorrect
                              ? 'bg-red-50 border-red-200 dark:bg-red-900/20'
                              : 'bg-gray-50 dark:bg-gray-800'
                        }`}
                      >
                        <div className="flex items-center">
                          {isCorrect && <CheckCircle className="h-4 w-4 text-green-600" />}
                          {isUserChoice && !isCorrect && <XCircle className="h-4 w-4 text-red-600" />}
                        </div>
                        <div className="flex-1">
                          <span className="font-medium mr-3">{choice})</span>
                          {currentQuestion[`choice${choice}` as keyof RepairQuestion] as string}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}

              {!questionAnswered && selectedAnswer && (
                <Button
                  onClick={handleAnswerSubmit}
                  disabled={isSubmitting}
                  className="w-full"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Submit Answer
                    </>
                  )}
                </Button>
              )}

              {questionAnswered && !answerCorrect && currentQuestion.explanation && (
                <div className="mt-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start gap-2">
                    <BookOpen className="h-4 w-4 text-blue-600 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">Explanation:</h4>
                      <p className="text-sm text-blue-800 dark:text-blue-200">
                        {currentQuestion.explanation}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Phase 2: Follow-up Questions to Test Understanding */}
        {currentPhase === 'follow_up' && currentFollowUp && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-blue-600" />
                Test Your Understanding - Question {currentFollowUpIndex + 1} of {followUpQuestions.length}
              </CardTitle>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                You got the answer right! Now let's make sure you truly understand why.
              </p>
            </CardHeader>
            <CardContent>
              <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 rounded-lg">
                <p className="text-sm text-green-800 dark:text-green-200">
                  <strong>Original Question:</strong> {currentQuestion.questionText}
                </p>
                <p className="text-sm text-green-800 dark:text-green-200 mt-1">
                  <strong>Your Correct Answer:</strong> {selectedAnswer}) {currentQuestion[`choice${selectedAnswer}` as keyof RepairQuestion] as string}
                </p>
              </div>

              <p className="text-gray-700 dark:text-gray-300 mb-4 text-lg">
                {currentFollowUp.questionText}
              </p>

              {currentFollowUp.expectedPoints && currentFollowUp.expectedPoints.length > 0 && (
                <div className="mb-4">
                  <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">
                    Consider addressing:
                  </h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    {currentFollowUp.expectedPoints.map((point, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <Target className="h-3 w-3 mt-1 text-blue-600 flex-shrink-0" />
                        {point}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <Textarea
                value={userExplanation}
                onChange={(e) => setUserExplanation(e.target.value)}
                placeholder="Explain your understanding in detail... Show that you truly comprehend the concept, not just memorized the answer."
                className="min-h-[150px] mb-4"
              />

              <Button
                onClick={handleFollowUpSubmit}
                disabled={!userExplanation.trim() || isSubmitting || isAnalyzing}
                className="w-full"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : isAnalyzing ? (
                  <>
                    <Brain className="h-4 w-4 mr-2" />
                    Analyzing Understanding...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Submit Explanation
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        )}

        {/* AI Analysis Results */}
        {currentAnalysis && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-blue-600" />
                AI Analysis - Score: {currentAnalysis.score}/10
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold text-green-700 dark:text-green-400 mb-2">Feedback:</h4>
                <p className="text-sm text-gray-700 dark:text-gray-300">{currentAnalysis.feedback}</p>
              </div>

              {currentAnalysis.correctPoints?.length > 0 && (
                <div>
                  <h4 className="font-semibold text-green-700 dark:text-green-400 mb-2">What you got right:</h4>
                  <ul className="text-sm space-y-1">
                    {currentAnalysis.correctPoints.map((point: string, index: number) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckCircle className="h-3 w-3 mt-1 text-green-600 flex-shrink-0" />
                        {point}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {currentAnalysis.missingPoints?.length > 0 && (
                <div>
                  <h4 className="font-semibold text-orange-700 dark:text-orange-400 mb-2">Points to improve:</h4>
                  <ul className="text-sm space-y-1">
                    {currentAnalysis.missingPoints.map((point: string, index: number) => (
                      <li key={index} className="flex items-start gap-2">
                        <XCircle className="h-3 w-3 mt-1 text-orange-600 flex-shrink-0" />
                        {point}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {currentAnalysis.suggestions?.length > 0 && (
                <div>
                  <h4 className="font-semibold text-blue-700 dark:text-blue-400 mb-2">Suggestions:</h4>
                  <ul className="text-sm space-y-1">
                    {currentAnalysis.suggestions.map((suggestion: string, index: number) => (
                      <li key={index} className="flex items-start gap-2">
                        <BookOpen className="h-3 w-3 mt-1 text-blue-600 flex-shrink-0" />
                        {suggestion}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Navigation */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePreviousQuestion}
            disabled={currentQuestionIndex === 0}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous Question
          </Button>

          {currentQuestionIndex < repairQuestions.length - 1 ? (
            <Button
              onClick={handleNextQuestion}
              disabled={!isCurrentSubmitted && !(questionAnswered && !answerCorrect)}
            >
              Next Question
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button
              onClick={onBack}
              disabled={!isCurrentSubmitted && !(questionAnswered && !answerCorrect)}
              className="bg-green-600 hover:bg-green-700"
            >
              Complete Session
              <CheckCircle className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
