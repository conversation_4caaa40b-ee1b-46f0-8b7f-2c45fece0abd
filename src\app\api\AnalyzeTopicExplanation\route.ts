import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      topic,
      userExplanation,
      expectedPoints,
      certificateName,
      relatedQuestions
    } = body;

    if (!topic || !userExplanation) {
      return NextResponse.json(
        { success: false, error: 'Topic and user explanation are required' },
        { status: 400 }
      );
    }

    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro" });

    const prompt = `
You are an expert ${certificateName || 'certification'} instructor analyzing a student's explanation of a topic they previously answered incorrectly.

TOPIC: ${topic}

STUDENT'S EXPLANATION: ${userExplanation}

${expectedPoints && expectedPoints.length > 0 ? `
EXPECTED KEY POINTS TO COVER:
${expectedPoints.map((point: string, index: number) => `${index + 1}. ${point}`).join('\n')}
` : ''}

${relatedQuestions && relatedQuestions.length > 0 ? `
CONTEXT - RELATED QUESTIONS THEY GOT WRONG:
${relatedQuestions.map((q: any, index: number) => `
Question ${index + 1}: ${q.questionText}
Their answer: ${q.userAnswer} (Correct: ${q.correctAnswer})
`).join('\n')}
` : ''}

ANALYSIS TASK:
Evaluate the student's understanding of this topic based on their explanation. Consider:

1. CONCEPTUAL UNDERSTANDING: Do they grasp the core concepts?
2. COMPLETENESS: Have they covered the essential aspects?
3. ACCURACY: Are their statements factually correct?
4. DEPTH: Do they show deep understanding or surface-level knowledge?
5. CONNECTIONS: Do they understand how this topic relates to other concepts?
6. PRACTICAL APPLICATION: Can they apply the knowledge correctly?

Provide your analysis in the following JSON format:
{
  "score": number, // 1-10 scale for overall understanding
  "feedback": "Detailed feedback on their understanding",
  "correctPoints": [
    "Point 1 they got right",
    "Point 2 they got right"
  ],
  "missingPoints": [
    "Important point 1 they missed",
    "Important point 2 they missed"
  ],
  "misconceptions": [
    "Misconception 1 if any",
    "Misconception 2 if any"
  ],
  "suggestions": [
    "Specific suggestion 1 for improvement",
    "Specific suggestion 2 for improvement",
    "Specific suggestion 3 for improvement"
  ],
  "studyRecommendations": [
    "Specific resource or topic to study",
    "Practice area to focus on"
  ],
  "strengthAreas": [
    "Area 1 where they show good understanding",
    "Area 2 where they show good understanding"
  ],
  "improvementAreas": [
    "Area 1 that needs work",
    "Area 2 that needs work"
  ]
}

Be constructive and specific in your feedback. Help the student understand exactly what they need to improve.`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    try {
      // Try to parse the JSON response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const analysisData = JSON.parse(jsonMatch[0]);
      
      // Validate the response structure
      if (typeof analysisData.score !== 'number' ||
          typeof analysisData.feedback !== 'string' ||
          !Array.isArray(analysisData.suggestions)) {
        throw new Error('Invalid response structure');
      }

      return NextResponse.json({
        success: true,
        data: {
          ...analysisData,
          topic,
          analysisMetadata: {
            analyzedAt: new Date().toISOString(),
            certificateName
          }
        }
      });

    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      
      // Fallback response if JSON parsing fails
      return NextResponse.json({
        success: true,
        data: {
          score: 5,
          feedback: text || "Unable to analyze explanation properly. Please try again.",
          correctPoints: [],
          missingPoints: [],
          misconceptions: [],
          suggestions: [
            "Review the key concepts related to this topic",
            "Practice explaining concepts in your own words",
            "Focus on understanding practical applications"
          ],
          studyRecommendations: [`Study ${topic} in more detail`],
          strengthAreas: [],
          improvementAreas: [topic],
          topic,
          analysisMetadata: {
            analyzedAt: new Date().toISOString(),
            certificateName
          }
        }
      });
    }

  } catch (error) {
    console.error('Error in AnalyzeTopicExplanation API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to analyze topic explanation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
