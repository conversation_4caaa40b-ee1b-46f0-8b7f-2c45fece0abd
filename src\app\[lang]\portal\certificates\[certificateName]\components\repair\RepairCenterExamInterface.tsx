"use client";

import React, { useState, useEffect } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import {
  createRepairCenterExamAttempt,
  addQuestionsToRepairCenterAttempt,
  submitRepairCenterAnswer,
  addAIAnalysisToQuestion,
  getRepairCenterExamQuestions,
  completeRepairCenterExamAttempt,
  RepairCenterExamQuestion,
  RepairCenterExamAttempt
} from "@/Firebase/firestore/services/RepairCenterExamService";
import { Question } from "@/Firebase/firestore/services/QuestionsService";
import { useAuth } from "@/context";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import {
  ArrowLeft,
  ArrowRight,
  Clock,
  MessageSquare,
  Brain,
  CheckCircle,
  XCircle,
  Loader2,
  Target,
  BookOpen,
  AlertTriangle
} from "lucide-react";

interface RepairCenterExamInterfaceProps {
  certificate: Certificate;
  repairQuestions: Array<{
    questionId: string;
    question: Question;
    priority: 'super-critical' | 'critical' | 'moderate' | 'low';
    successRate: number;
    topics: string[];
  }>;
  gameType: 'multiple_choice' | 'flashcards' | 'true_false' | 'fill_blank' | 'matching' | 'concept_mapping';
  onBack: () => void;
  onComplete: (attempt: RepairCenterExamAttempt) => void;
}

export default function RepairCenterExamInterface({
  certificate,
  repairQuestions,
  gameType,
  onBack,
  onComplete
}: RepairCenterExamInterfaceProps) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<'A' | 'B' | 'C' | 'D' | null>(null);
  const [userExplanation, setUserExplanation] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [attemptId, setAttemptId] = useState<string | null>(null);
  const [examQuestions, setExamQuestions] = useState<RepairCenterExamQuestion[]>([]);
  const [submittedQuestions, setSubmittedQuestions] = useState<Set<number>>(new Set());
  const [aiAnalyses, setAiAnalyses] = useState<Map<number, any>>(new Map());
  const [startTime] = useState(Date.now());
  const [questionStartTime, setQuestionStartTime] = useState(Date.now());
  const [showExplanationInput, setShowExplanationInput] = useState(false);

  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    initializeExam();
  }, []);

  const initializeExam = async () => {
    if (!user?.uid || !certificate.id) return;

    try {
      // Create repair center exam attempt
      const topics = [...new Set(repairQuestions.flatMap(q => q.topics))];
      const sourceQuestionIds = repairQuestions.map(q => q.questionId);
      
      const attemptId = await createRepairCenterExamAttempt(
        user.uid,
        certificate.id,
        'repair_center',
        repairQuestions.length,
        sourceQuestionIds,
        topics,
        gameType
      );
      setAttemptId(attemptId);

      // Convert repair questions to exam questions
      const examQuestions: Omit<RepairCenterExamQuestion, 'id'>[] = repairQuestions.map((repairQ, index) => ({
        questionId: repairQ.questionId,
        questionText: repairQ.question.question,
        choiceA: repairQ.question.choiceA,
        choiceB: repairQ.question.choiceB,
        choiceC: repairQ.question.choiceC,
        choiceD: repairQ.question.choiceD,
        correctAnswer: repairQ.question.correctAnswer,
        topic: repairQ.question.category,
        explanation: repairQ.question.explanation,
        orderIndex: index,
        priority: repairQ.priority,
        originalSuccessRate: repairQ.successRate
      }));

      await addQuestionsToRepairCenterAttempt(attemptId, examQuestions);
      
      // Fetch the created questions with IDs
      const createdQuestions = await getRepairCenterExamQuestions(attemptId);
      setExamQuestions(createdQuestions);

    } catch (error) {
      console.error('Error initializing repair center exam:', error);
      toast({
        title: "Error",
        description: "Failed to initialize repair center exam.",
        variant: "destructive",
      });
    }
  };

  const handleAnswerSelect = (answer: 'A' | 'B' | 'C' | 'D') => {
    setSelectedAnswer(answer);
    setShowExplanationInput(true);
  };

  const handleSubmitAnswer = async () => {
    if (!attemptId || !selectedAnswer || !examQuestions[currentQuestionIndex]?.id) return;

    try {
      setIsSubmitting(true);
      const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);
      const currentQuestion = examQuestions[currentQuestionIndex];

      // Submit the answer
      const isCorrect = await submitRepairCenterAnswer(
        attemptId,
        currentQuestion.id!,
        selectedAnswer,
        timeSpent,
        userExplanation
      );

      // Analyze explanation with AI if provided
      if (userExplanation.trim()) {
        setIsAnalyzing(true);
        try {
          const response = await fetch('/api/AnalyzeRepairCenterExplanation', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              question: currentQuestion.questionText,
              choiceA: currentQuestion.choiceA,
              choiceB: currentQuestion.choiceB,
              choiceC: currentQuestion.choiceC,
              choiceD: currentQuestion.choiceD,
              correctAnswer: currentQuestion.correctAnswer,
              userAnswer: selectedAnswer,
              userExplanation,
              topic: currentQuestion.topic,
              certificateName: certificate.name,
              priority: currentQuestion.priority,
              originalSuccessRate: currentQuestion.originalSuccessRate
            })
          });

          if (response.ok) {
            const result = await response.json();
            if (result.success) {
              await addAIAnalysisToQuestion(
                attemptId,
                currentQuestion.id!,
                {
                  isExplanationCorrect: result.data.isExplanationCorrect,
                  feedback: result.data.feedback,
                  suggestions: result.data.suggestions,
                  score: result.data.score
                }
              );

              setAiAnalyses(prev => new Map(prev.set(currentQuestionIndex, result.data)));
            }
          }
        } catch (analysisError) {
          console.error('Error analyzing explanation:', analysisError);
        }
        setIsAnalyzing(false);
      }

      setSubmittedQuestions(prev => new Set(prev.add(currentQuestionIndex)));

      toast({
        title: isCorrect ? "Correct!" : "Incorrect",
        description: isCorrect 
          ? "Great job! Your answer is correct." 
          : `The correct answer was ${currentQuestion.correctAnswer}.`,
        variant: isCorrect ? "default" : "destructive",
      });

    } catch (error) {
      console.error('Error submitting answer:', error);
      toast({
        title: "Error",
        description: "Failed to submit answer.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < examQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setSelectedAnswer(null);
      setUserExplanation("");
      setShowExplanationInput(false);
      setQuestionStartTime(Date.now());
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
      setSelectedAnswer(null);
      setUserExplanation("");
      setShowExplanationInput(false);
      setQuestionStartTime(Date.now());
    }
  };

  const handleCompleteExam = async () => {
    if (!attemptId) return;

    try {
      const completedAttempt = await completeRepairCenterExamAttempt(attemptId);
      onComplete(completedAttempt);
    } catch (error) {
      console.error('Error completing exam:', error);
      toast({
        title: "Error",
        description: "Failed to complete exam.",
        variant: "destructive",
      });
    }
  };

  const currentQuestion = examQuestions[currentQuestionIndex];
  const currentAnalysis = aiAnalyses.get(currentQuestionIndex);
  const isCurrentSubmitted = submittedQuestions.has(currentQuestionIndex);

  if (!currentQuestion) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading exam questions...</p>
        </div>
      </div>
    );
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'super-critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'critical': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'moderate': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Button variant="ghost" onClick={onBack} className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Repair Center
          </Button>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="flex items-center gap-1">
              <Target className="h-3 w-3" />
              Question {currentQuestionIndex + 1} of {examQuestions.length}
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {Math.floor((Date.now() - startTime) / 60000)}m
            </Badge>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
            <span>Progress</span>
            <span>{submittedQuestions.size} / {examQuestions.length} answered</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(submittedQuestions.size / examQuestions.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Current Question */}
        <Card className="mb-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-blue-600" />
                Repair Center Question
              </CardTitle>
              <div className="flex items-center gap-2">
                <Badge className={getPriorityColor(currentQuestion.priority || 'low')}>
                  {currentQuestion.priority || 'low'} priority
                </Badge>
                {currentQuestion.originalSuccessRate !== undefined && (
                  <Badge variant="outline">
                    {currentQuestion.originalSuccessRate.toFixed(1)}% success rate
                  </Badge>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 dark:text-gray-300 mb-6 text-lg">
              {currentQuestion.questionText}
            </p>
            
            {!isCurrentSubmitted && (
              <RadioGroup value={selectedAnswer || ""} onValueChange={handleAnswerSelect}>
                <div className="space-y-3">
                  {(['A', 'B', 'C', 'D'] as const).map((choice) => (
                    <div key={choice} className="flex items-center space-x-2 p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800">
                      <RadioGroupItem value={choice} id={choice} />
                      <Label htmlFor={choice} className="flex-1 cursor-pointer">
                        <span className="font-medium mr-2">{choice})</span>
                        {currentQuestion[`choice${choice}` as keyof RepairCenterExamQuestion] as string}
                      </Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            )}

            {isCurrentSubmitted && (
              <div className="space-y-3">
                {(['A', 'B', 'C', 'D'] as const).map((choice) => {
                  const isCorrect = choice === currentQuestion.correctAnswer;
                  const isUserChoice = choice === examQuestions[currentQuestionIndex].userAnswer;
                  
                  return (
                    <div 
                      key={choice} 
                      className={`flex items-center space-x-2 p-3 rounded-lg border ${
                        isCorrect 
                          ? 'bg-green-50 border-green-200 dark:bg-green-900/20' 
                          : isUserChoice 
                            ? 'bg-red-50 border-red-200 dark:bg-red-900/20'
                            : 'bg-gray-50 dark:bg-gray-800'
                      }`}
                    >
                      <div className="flex items-center">
                        {isCorrect && <CheckCircle className="h-4 w-4 text-green-600" />}
                        {isUserChoice && !isCorrect && <XCircle className="h-4 w-4 text-red-600" />}
                      </div>
                      <Label className="flex-1">
                        <span className="font-medium mr-2">{choice})</span>
                        {currentQuestion[`choice${choice}` as keyof RepairCenterExamQuestion] as string}
                      </Label>
                    </div>
                  );
                })}
              </div>
            )}

            {/* Explanation Input */}
            {showExplanationInput && !isCurrentSubmitted && (
              <div className="mt-6">
                <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                  Explain why you think this is the correct answer:
                </Label>
                <Textarea
                  value={userExplanation}
                  onChange={(e) => setUserExplanation(e.target.value)}
                  placeholder="Explain your reasoning for choosing this answer..."
                  className="min-h-[100px]"
                />
              </div>
            )}

            {!isCurrentSubmitted && selectedAnswer && (
              <Button
                onClick={handleSubmitAnswer}
                disabled={isSubmitting || isAnalyzing}
                className="w-full mt-4"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : isAnalyzing ? (
                  <>
                    <Brain className="h-4 w-4 mr-2" />
                    Analyzing...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Submit Answer
                  </>
                )}
              </Button>
            )}
          </CardContent>
        </Card>

        {/* AI Analysis Results */}
        {currentAnalysis && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-purple-600" />
                AI Analysis - Reasoning Score: {currentAnalysis.score}/10
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className={`p-3 rounded-lg ${
                currentAnalysis.isExplanationCorrect 
                  ? 'bg-green-50 border border-green-200 dark:bg-green-900/20' 
                  : 'bg-orange-50 border border-orange-200 dark:bg-orange-900/20'
              }`}>
                <div className="flex items-center gap-2 mb-2">
                  {currentAnalysis.isExplanationCorrect ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-orange-600" />
                  )}
                  <span className="font-medium">
                    {currentAnalysis.isExplanationCorrect ? 'Sound Reasoning' : 'Reasoning Needs Work'}
                  </span>
                </div>
                <p className="text-sm">{currentAnalysis.feedback}</p>
              </div>

              {currentAnalysis.suggestions?.length > 0 && (
                <div>
                  <h4 className="font-semibold text-blue-700 dark:text-blue-400 mb-2">Suggestions for improvement:</h4>
                  <ul className="text-sm space-y-1">
                    {currentAnalysis.suggestions.map((suggestion: string, index: number) => (
                      <li key={index} className="flex items-start gap-2">
                        <BookOpen className="h-3 w-3 mt-1 text-blue-600 flex-shrink-0" />
                        {suggestion}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Navigation */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePreviousQuestion}
            disabled={currentQuestionIndex === 0}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          {currentQuestionIndex < examQuestions.length - 1 ? (
            <Button
              onClick={handleNextQuestion}
              disabled={!isCurrentSubmitted}
            >
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button
              onClick={handleCompleteExam}
              disabled={submittedQuestions.size < examQuestions.length}
              className="bg-green-600 hover:bg-green-700"
            >
              Complete Exam
              <CheckCircle className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
