"use client";

import React, { useState, useEffect } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  XCircle,
  Clock,
  Target,
  AlertTriangle,
  BookOpen
} from "lucide-react";

interface TrueFalseItem {
  id: string;
  statement: string;
  isTrue: boolean;
  explanation: string;
  topic: string;
  difficulty: 'easy' | 'medium' | 'hard';
  commonMisconception?: string;
}

interface TrueFalseGameProps {
  certificate: Certificate;
  topics: string[];
  repairCenterQuestions?: Array<{
    questionId: string;
    question: any;
    priority: string;
    successRate: number;
    topics: string[];
  }>;
  onBack: () => void;
}

export default function TrueFalseGame({
  certificate,
  topics,
  repairCenterQuestions = [],
  onBack
}: TrueFalseGameProps) {
  const [statements, setStatements] = useState<TrueFalseItem[]>([]);
  const [currentStatementIndex, setCurrentStatementIndex] = useState(0);
  const [userAnswer, setUserAnswer] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showResult, setShowResult] = useState(false);
  const [answers, setAnswers] = useState<Map<number, boolean>>(new Map());
  const [sessionStartTime] = useState(Date.now());
  const [statementStartTime, setStatementStartTime] = useState(Date.now());

  const { toast } = useToast();

  useEffect(() => {
    generateStatements();
  }, []);

  const generateStatements = async () => {
    try {
      setIsLoading(true);

      const response = await fetch('/api/GenerateRepairCenterExam', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          topics,
          certificateName: certificate.name,
          gameType: 'true_false',
          itemCount: 12,
          difficulty: 'medium',
          repairCenterQuestions: repairCenterQuestions // Pass the actual questions
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data.items) {
          setStatements(result.data.items);
        } else {
          throw new Error('Failed to generate statements');
        }
      } else {
        throw new Error('API request failed');
      }
    } catch (error) {
      console.error('Error generating statements:', error);
      toast({
        title: "Error",
        description: "Failed to generate true/false statements. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAnswer = (answer: boolean) => {
    setUserAnswer(answer);
    setAnswers(prev => new Map(prev.set(currentStatementIndex, answer)));
    setShowResult(true);

    const isCorrect = answer === statements[currentStatementIndex].isTrue;
    
    toast({
      title: isCorrect ? "Correct!" : "Incorrect",
      description: isCorrect 
        ? "Great job! Your answer is correct." 
        : `The correct answer is ${statements[currentStatementIndex].isTrue ? 'True' : 'False'}.`,
      variant: isCorrect ? "default" : "destructive",
    });
  };

  const handleNext = () => {
    if (currentStatementIndex < statements.length - 1) {
      setCurrentStatementIndex(currentStatementIndex + 1);
      setUserAnswer(null);
      setShowResult(false);
      setStatementStartTime(Date.now());
    }
  };

  const handlePrevious = () => {
    if (currentStatementIndex > 0) {
      setCurrentStatementIndex(currentStatementIndex - 1);
      const prevAnswer = answers.get(currentStatementIndex - 1);
      setUserAnswer(prevAnswer ?? null);
      setShowResult(prevAnswer !== undefined);
      setStatementStartTime(Date.now());
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800 border-green-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'hard': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getScore = () => {
    let correct = 0;
    answers.forEach((userAnswer, index) => {
      if (userAnswer === statements[index].isTrue) {
        correct++;
      }
    });
    return { correct, total: answers.size };
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p>Generating true/false statements...</p>
        </div>
      </div>
    );
  }

  if (statements.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 flex items-center justify-center">
        <div className="text-center">
          <p className="mb-4">No statements available.</p>
          <Button onClick={onBack}>Back to Repair Center</Button>
        </div>
      </div>
    );
  }

  const currentStatement = statements[currentStatementIndex];
  const progress = ((currentStatementIndex + 1) / statements.length) * 100;
  const sessionTime = Math.floor((Date.now() - sessionStartTime) / 60000);
  const score = getScore();

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20">
      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Button variant="ghost" onClick={onBack} className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Repair Center
          </Button>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="flex items-center gap-1">
              <Target className="h-3 w-3" />
              {currentStatementIndex + 1} of {statements.length}
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {sessionTime}m
            </Badge>
            {score.total > 0 && (
              <Badge variant="outline">
                Score: {score.correct}/{score.total}
              </Badge>
            )}
          </div>
        </div>

        {/* Progress */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
            <span>Progress</span>
            <span>{Math.round(progress)}% complete</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-purple-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* Current Statement */}
        <Card className="mb-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-purple-600" />
                True or False?
              </CardTitle>
              <div className="flex items-center gap-2">
                <Badge className={getDifficultyColor(currentStatement.difficulty)}>
                  {currentStatement.difficulty}
                </Badge>
                <Badge variant="outline">
                  {currentStatement.topic}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-center mb-8">
              <p className="text-xl font-medium text-gray-900 dark:text-white mb-6 leading-relaxed">
                {currentStatement.statement}
              </p>

              {!showResult && (
                <div className="flex justify-center gap-6">
                  <Button
                    size="lg"
                    onClick={() => handleAnswer(true)}
                    className="bg-green-600 hover:bg-green-700 text-white px-8 py-4 text-lg"
                  >
                    <CheckCircle className="h-5 w-5 mr-2" />
                    TRUE
                  </Button>
                  <Button
                    size="lg"
                    variant="destructive"
                    onClick={() => handleAnswer(false)}
                    className="px-8 py-4 text-lg"
                  >
                    <XCircle className="h-5 w-5 mr-2" />
                    FALSE
                  </Button>
                </div>
              )}

              {showResult && (
                <div className="space-y-4">
                  <div className={`p-4 rounded-lg ${
                    userAnswer === currentStatement.isTrue 
                      ? 'bg-green-50 border border-green-200 dark:bg-green-900/20' 
                      : 'bg-red-50 border border-red-200 dark:bg-red-900/20'
                  }`}>
                    <div className="flex items-center justify-center gap-2 mb-2">
                      {userAnswer === currentStatement.isTrue ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                      <span className="font-medium">
                        {userAnswer === currentStatement.isTrue ? 'Correct!' : 'Incorrect'}
                      </span>
                    </div>
                    <p className="text-sm">
                      The correct answer is: <strong>{currentStatement.isTrue ? 'TRUE' : 'FALSE'}</strong>
                    </p>
                  </div>

                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start gap-2">
                      <BookOpen className="h-4 w-4 text-blue-600 mt-1 flex-shrink-0" />
                      <div>
                        <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">Explanation:</h4>
                        <p className="text-sm text-blue-800 dark:text-blue-200">
                          {currentStatement.explanation}
                        </p>
                      </div>
                    </div>
                  </div>

                  {currentStatement.commonMisconception && (
                    <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 rounded-lg p-4">
                      <div className="flex items-start gap-2">
                        <AlertTriangle className="h-4 w-4 text-orange-600 mt-1 flex-shrink-0" />
                        <div>
                          <h4 className="font-medium text-orange-900 dark:text-orange-100 mb-1">Common Misconception:</h4>
                          <p className="text-sm text-orange-800 dark:text-orange-200">
                            {currentStatement.commonMisconception}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStatementIndex === 0}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          {currentStatementIndex < statements.length - 1 ? (
            <Button
              onClick={handleNext}
              disabled={!showResult}
            >
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button
              onClick={onBack}
              disabled={!showResult}
              className="bg-green-600 hover:bg-green-700"
            >
              Complete
              <CheckCircle className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>

        {/* Final Results */}
        {currentStatementIndex === statements.length - 1 && showResult && (
          <Card className="mt-6 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border-purple-200">
            <CardContent className="p-6 text-center">
              <h3 className="text-lg font-bold text-purple-900 dark:text-purple-100 mb-4">
                Session Complete! 🎉
              </h3>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{score.correct}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Correct</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{score.total - score.correct}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Incorrect</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {Math.round((score.correct / score.total) * 100)}%
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Accuracy</div>
                </div>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Session Time: {sessionTime} minutes
              </p>
              <Button onClick={onBack}>
                Back to Repair Center
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
