import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      topics, 
      certificateName,
      gameType,
      difficulty = 'medium',
      itemCount = 10,
      repairCenterQuestions = []
    } = body;

    if (!topics || !Array.isArray(topics) || topics.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Topics array is required' },
        { status: 400 }
      );
    }

    if (!gameType) {
      return NextResponse.json(
        { success: false, error: 'Game type is required' },
        { status: 400 }
      );
    }

    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro" });

    const contextInfo = repairCenterQuestions.length > 0 
      ? `\n\nCONTEXT - Questions the student got wrong:\n${repairCenterQuestions.map((q: any, index: number) => 
          `${index + 1}. ${q.questionText}\n   Correct Answer: ${q.correctAnswer}\n   Topic: ${q.topic || 'Not specified'}`
        ).join('\n\n')}`
      : '';

    let prompt = '';
    
    switch (gameType) {
      case 'flashcards':
        prompt = `
Create ${itemCount} flashcards for ${certificateName} focusing on: ${topics.join(', ')}

These flashcards should help reinforce understanding of concepts the student struggled with.${contextInfo}

Each flashcard should:
- Focus on key concepts from the specified topics
- Help clarify common misconceptions
- Be at ${difficulty} difficulty level
- Include practical applications where relevant

Return as JSON:
{
  "gameType": "flashcards",
  "items": [
    {
      "id": "unique_id",
      "front": "Question or concept prompt",
      "back": "Answer or explanation",
      "topic": "specific topic",
      "difficulty": "${difficulty}",
      "hints": ["hint1", "hint2"]
    }
  ],
  "instructions": "Review each flashcard carefully and test your understanding",
  "studyTips": ["tip1", "tip2", "tip3"]
}`;
        break;

      case 'multiple_choice':
        prompt = `
Create ${itemCount} multiple choice questions for ${certificateName} focusing on: ${topics.join(', ')}

These questions should help reinforce understanding of concepts the student struggled with.${contextInfo}

Each question should:
- Test understanding of key concepts
- Include plausible distractors that address common misconceptions
- Be at ${difficulty} difficulty level
- Have clear explanations for why each answer is correct/incorrect

Return as JSON:
{
  "gameType": "multiple_choice",
  "items": [
    {
      "id": "unique_id",
      "questionText": "The question",
      "choiceA": "Option A",
      "choiceB": "Option B", 
      "choiceC": "Option C",
      "choiceD": "Option D",
      "correctAnswer": "A",
      "explanation": "Why this answer is correct",
      "topic": "specific topic",
      "difficulty": "${difficulty}"
    }
  ],
  "instructions": "Choose the best answer for each question",
  "studyTips": ["tip1", "tip2", "tip3"]
}`;
        break;

      case 'true_false':
        prompt = `
Create ${itemCount} true/false statements for ${certificateName} focusing on: ${topics.join(', ')}

These statements should help clarify misconceptions and reinforce correct understanding.${contextInfo}

Each statement should:
- Test specific factual knowledge or conceptual understanding
- Address common misconceptions
- Be at ${difficulty} difficulty level
- Include clear explanations

Return as JSON:
{
  "gameType": "true_false",
  "items": [
    {
      "id": "unique_id",
      "statement": "The statement to evaluate",
      "isTrue": true,
      "explanation": "Why this statement is true/false",
      "topic": "specific topic",
      "difficulty": "${difficulty}",
      "commonMisconception": "What misconception this addresses"
    }
  ],
  "instructions": "Determine if each statement is true or false",
  "studyTips": ["tip1", "tip2", "tip3"]
}`;
        break;

      case 'fill_blank':
        prompt = `
Create ${itemCount} fill-in-the-blank exercises for ${certificateName} focusing on: ${topics.join(', ')}

These exercises should help reinforce key terminology and concepts.${contextInfo}

Each exercise should:
- Test important terminology or key facts
- Have clear context clues
- Be at ${difficulty} difficulty level
- Include multiple choice options for the blanks

Return as JSON:
{
  "gameType": "fill_blank",
  "items": [
    {
      "id": "unique_id",
      "sentence": "The sentence with _____ blanks to fill",
      "blanks": ["correct answer 1", "correct answer 2"],
      "options": ["option1", "option2", "option3", "option4"],
      "topic": "specific topic",
      "difficulty": "${difficulty}",
      "explanation": "Why these answers are correct"
    }
  ],
  "instructions": "Fill in the blanks with the most appropriate terms",
  "studyTips": ["tip1", "tip2", "tip3"]
}`;
        break;

      case 'matching':
        prompt = `
Create ${Math.ceil(itemCount/5)} matching exercises for ${certificateName} focusing on: ${topics.join(', ')}

Each exercise should have 5-8 items to match. These should help connect related concepts.${contextInfo}

Each exercise should:
- Connect related terms, concepts, or examples
- Help understand relationships between ideas
- Be at ${difficulty} difficulty level
- Cover important associations

Return as JSON:
{
  "gameType": "matching",
  "items": [
    {
      "id": "unique_id",
      "leftItems": ["Term 1", "Term 2", "Term 3", "Term 4", "Term 5"],
      "rightItems": ["Definition 1", "Definition 2", "Definition 3", "Definition 4", "Definition 5"],
      "correctMatches": [
        {"left": 0, "right": 0},
        {"left": 1, "right": 1}
      ],
      "topic": "specific topic",
      "difficulty": "${difficulty}",
      "explanation": "Why these matches are correct"
    }
  ],
  "instructions": "Match each item on the left with its corresponding item on the right",
  "studyTips": ["tip1", "tip2", "tip3"]
}`;
        break;

      case 'concept_mapping':
        prompt = `
Create ${Math.ceil(itemCount/3)} concept mapping exercises for ${certificateName} focusing on: ${topics.join(', ')}

These should help students understand relationships between concepts.${contextInfo}

Each exercise should:
- Show how concepts relate to each other
- Help understand hierarchies and connections
- Be at ${difficulty} difficulty level
- Include clear relationship descriptions

Return as JSON:
{
  "gameType": "concept_mapping",
  "items": [
    {
      "id": "unique_id",
      "centralConcept": "Main concept",
      "relatedConcepts": ["concept1", "concept2", "concept3"],
      "relationships": [
        {"from": "concept1", "to": "centralConcept", "relationship": "is a type of"},
        {"from": "concept2", "to": "centralConcept", "relationship": "leads to"}
      ],
      "topic": "specific topic",
      "difficulty": "${difficulty}",
      "explanation": "How these concepts relate"
    }
  ],
  "instructions": "Map the relationships between concepts",
  "studyTips": ["tip1", "tip2", "tip3"]
}`;
        break;

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid game type' },
          { status: 400 }
        );
    }

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    try {
      // Try to parse the JSON response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const gameData = JSON.parse(jsonMatch[0]);
      
      return NextResponse.json({
        success: true,
        data: gameData
      });

    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      
      // Fallback response based on game type
      const fallbackData = {
        gameType,
        items: [],
        instructions: `Practice with these ${gameType} to improve your understanding.`,
        studyTips: [
          "Review each item carefully",
          "Focus on understanding rather than memorization",
          "Practice regularly for best results"
        ]
      };

      return NextResponse.json({
        success: true,
        data: fallbackData
      });
    }

  } catch (error) {
    console.error('Error in GenerateRepairCenterExam API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate repair center exam',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
