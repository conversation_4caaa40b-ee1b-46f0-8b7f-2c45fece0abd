"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import { getCertificateQuestions, Question } from "@/Firebase/firestore/services/QuestionsService";
import { getUserExamAttempts, getExamQuestions } from "@/Firebase/firestore/services/ExamService";
import { getIncorrectGameQuestions, getIncorrectQuestionsFromActiveGames } from "@/Firebase/firestore/services/GameService";
import { getIncorrectAnswerFullBankQuestions } from "@/Firebase/firestore/services/AnswerFullBankService";
import { useAuth } from "@/context";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import TopicExplanationMode from "./repair/TopicExplanationMode";
import RepairCenterExamInterface from "./repair/RepairCenterExamInterface";
import FlashcardsGame from "./repair/games/FlashcardsGame";
import TrueFalseGame from "./repair/games/TrueFalseGame";
import MatchingGame from "./repair/games/MatchingGame";
import {
  AlertTriangle,
  Target,
  CheckCircle,
  XCircle,
  Loader2,
  Search,
  BookOpen,
  TrendingDown,
  RefreshCw,
  GraduationCap,
  Brain,
  Gamepad2,
  FileText,
  MessageSquare,
  Zap
} from "lucide-react";

interface RepairCenterTabProps {
  certificate: Certificate;
}

interface QuestionRepairData {
  questionId: string;
  question: Question;
  totalAttempts: number;
  correctAttempts: number;
  incorrectAttempts: number;
  successRate: number;
  priority: 'super-critical' | 'critical' | 'moderate' | 'low';
  lastAttemptCorrect: boolean;
  topics: string[];
  averageTimeSpent: number;
  recentPerformance: boolean[]; // Last 5 attempts
}

type FilterType = 'all' | 'super-critical' | 'critical' | 'moderate' | 'low';
type SortType = 'priority' | 'success-rate' | 'attempts' | 'recent';

export default function RepairCenterTab({ certificate }: RepairCenterTabProps) {
  const [repairData, setRepairData] = useState<QuestionRepairData[]>([]);
  const [filteredData, setFilteredData] = useState<QuestionRepairData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedFilter, setSelectedFilter] = useState<FilterType>('all');
  const [selectedSort, setSelectedSort] = useState<SortType>('priority');
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedQuestions, setExpandedQuestions] = useState<Set<string>>(new Set());
  const [showExamOptions, setShowExamOptions] = useState(false);
  const [isCreatingExam, setIsCreatingExam] = useState(false);
  const [currentView, setCurrentView] = useState<'repair_center' | 'exam' | 'topic_explanation' | 'flashcards' | 'true_false' | 'matching'>('repair_center');
  const [selectedGameType, setSelectedGameType] = useState<string>('');
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(false);

  const { user } = useAuth();
  const { toast } = useToast();

  // Exam creation functions
  const handleCreateRepairExam = async (gameType: string) => {
    if (!user?.uid || !certificate.id || filteredData.length === 0) return;

    try {
      setIsCreatingExam(true);

      // Get topics from filtered repair data
      const topics = [...new Set(filteredData.flatMap(item => item.topics))];

      setSelectedGameType(gameType);

      // Navigate to appropriate game interface
      if (gameType === 'multiple_choice') {
        setCurrentView('exam');
      } else if (gameType === 'flashcards') {
        setCurrentView('flashcards');
      } else if (gameType === 'true_false') {
        setCurrentView('true_false');
      } else if (gameType === 'matching') {
        setCurrentView('matching');
      } else {
        // For other game types, show a message for now
        toast({
          title: "Coming Soon",
          description: `${gameType} mode is coming soon!`,
        });
      }

    } catch (error) {
      console.error('Error creating repair exam:', error);
      toast({
        title: "Error",
        description: "Failed to create repair center exam. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreatingExam(false);
    }
  };

  const handleCreateTopicExplanationMode = async () => {
    if (!user?.uid || !certificate.id || filteredData.length === 0) return;

    try {
      setIsCreatingExam(true);

      // Get unique topics from filtered repair data
      const topics = [...new Set(filteredData.flatMap(item => item.topics))];

      setCurrentView('topic_explanation');

    } catch (error) {
      console.error('Error creating topic explanation mode:', error);
      toast({
        title: "Error",
        description: "Failed to create topic explanation mode. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreatingExam(false);
    }
  };

  const handleBackToRepairCenter = () => {
    setCurrentView('repair_center');
    setSelectedGameType('');
  };

  const loadRepairData = useCallback(async () => {
    if (!user?.uid || !certificate.id) return;

    try {
      setIsLoading(true);
      
      // Get all questions, exam attempts, game results, and AnswerFull Bank results
      const [questions, attempts, gameQuestions, activeGameQuestions, answerFullBankQuestions] = await Promise.all([
        getCertificateQuestions(user.uid, certificate.id),
        getUserExamAttempts(user.uid, certificate.id),
        getIncorrectGameQuestions(user.uid, certificate.id),
        getIncorrectQuestionsFromActiveGames(user.uid, certificate.id),
        getIncorrectAnswerFullBankQuestions(user.uid, certificate.id)
      ]);

      // Combine game questions from completed and active games
      const allGameQuestions = [...gameQuestions, ...activeGameQuestions];

      const completedAttempts = attempts.filter(a => a.status === 'completed');

      if (completedAttempts.length === 0 && allGameQuestions.length === 0 && answerFullBankQuestions.length === 0) {
        setRepairData([]);
        return;
      }

      // Get all exam questions from all attempts
      const allExamQuestions = [];
      for (const attempt of completedAttempts) {
        if (attempt.id) {
          const examQuestions = await getExamQuestions(attempt.id);
          allExamQuestions.push(...examQuestions.map(q => ({ ...q, attemptId: attempt.id })));
        }
      }

      // Analyze each question's performance
      const questionStats = new Map<string, {
        totalAttempts: number;
        correctAttempts: number;
        incorrectAttempts: number;
        timeSpent: number[];
        recentPerformance: boolean[];
        lastAttemptCorrect: boolean;
      }>();

      // Process all exam questions
      allExamQuestions.forEach(examQ => {
        if (!examQ.questionId || examQ.userAnswer === undefined) return;

        const stats = questionStats.get(examQ.questionId) || {
          totalAttempts: 0,
          correctAttempts: 0,
          incorrectAttempts: 0,
          timeSpent: [],
          recentPerformance: [],
          lastAttemptCorrect: false
        };

        stats.totalAttempts++;
        if (examQ.isCorrect) {
          stats.correctAttempts++;
        } else {
          stats.incorrectAttempts++;
        }
        
        if (examQ.timeSpent) {
          stats.timeSpent.push(examQ.timeSpent);
        }
        
        stats.recentPerformance.push(examQ.isCorrect || false);
        stats.lastAttemptCorrect = examQ.isCorrect || false;
        
        // Keep only last 5 performances
        if (stats.recentPerformance.length > 5) {
          stats.recentPerformance = stats.recentPerformance.slice(-5);
        }

        questionStats.set(examQ.questionId, stats);
      });

      // Process game questions (only incorrect ones are returned, including from active games)
      allGameQuestions.forEach(gameQ => {
        if (!gameQ.questionId) return;

        const stats = questionStats.get(gameQ.questionId) || {
          totalAttempts: 0,
          correctAttempts: 0,
          incorrectAttempts: 0,
          timeSpent: [],
          recentPerformance: [],
          lastAttemptCorrect: false
        };

        stats.totalAttempts++;
        stats.incorrectAttempts++; // All game questions returned are incorrect

        if (gameQ.timeSpent) {
          stats.timeSpent.push(gameQ.timeSpent);
        }

        stats.recentPerformance.push(false); // All are incorrect
        stats.lastAttemptCorrect = false;

        // Keep only last 5 performances
        if (stats.recentPerformance.length > 5) {
          stats.recentPerformance = stats.recentPerformance.slice(-5);
        }

        questionStats.set(gameQ.questionId, stats);
      });

      // Process AnswerFull Bank questions (only incorrect ones are returned)
      answerFullBankQuestions.forEach(bankQ => {
        if (!bankQ.questionId) return;

        const stats = questionStats.get(bankQ.questionId) || {
          totalAttempts: 0,
          correctAttempts: 0,
          incorrectAttempts: 0,
          timeSpent: [],
          recentPerformance: [],
          lastAttemptCorrect: false
        };

        stats.totalAttempts++;
        stats.incorrectAttempts++; // All AnswerFull Bank questions returned are incorrect

        if (bankQ.timeSpent) {
          stats.timeSpent.push(bankQ.timeSpent);
        }

        stats.recentPerformance.push(false); // All are incorrect
        stats.lastAttemptCorrect = false;

        // Keep only last 5 performances
        if (stats.recentPerformance.length > 5) {
          stats.recentPerformance = stats.recentPerformance.slice(-5);
        }

        questionStats.set(bankQ.questionId, stats);
      });

      // Create repair data for questions that have been answered incorrectly
      const repairQuestions: QuestionRepairData[] = [];
      
      questionStats.forEach((stats, questionId) => {
        // Only include questions that have been answered incorrectly at least once
        if (stats.incorrectAttempts > 0) {
          const question = questions.find(q => q.id === questionId);
          if (!question) return;

          const successRate = (stats.correctAttempts / stats.totalAttempts) * 100;
          const averageTimeSpent = stats.timeSpent.length > 0 
            ? stats.timeSpent.reduce((a, b) => a + b, 0) / stats.timeSpent.length 
            : 0;

          // Determine priority based on performance
          let priority: QuestionRepairData['priority'];
          if (stats.correctAttempts === 0) {
            priority = 'super-critical'; // Never got it right
          } else if (successRate < 30) {
            priority = 'critical'; // Very low success rate
          } else if (successRate < 60) {
            priority = 'moderate'; // Moderate success rate
          } else {
            priority = 'low'; // High success rate but still got it wrong sometimes
          }

          repairQuestions.push({
            questionId,
            question,
            totalAttempts: stats.totalAttempts,
            correctAttempts: stats.correctAttempts,
            incorrectAttempts: stats.incorrectAttempts,
            successRate,
            priority,
            lastAttemptCorrect: stats.lastAttemptCorrect,
            topics: question.category ? [question.category] : [],
            averageTimeSpent,
            recentPerformance: stats.recentPerformance
          });
        }
      });

      setRepairData(repairQuestions);
      
    } catch (error) {
      console.error('Error loading repair data:', error);
      toast({
        title: "Error",
        description: "Failed to load repair center data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [user?.uid, certificate.id, toast]);

  const filterAndSortData = useCallback(() => {
    let filtered = repairData;

    // Apply filter
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(item => item.priority === selectedFilter);
    }

    // Apply search
    if (searchQuery) {
      filtered = filtered.filter(item => 
        item.question.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.topics.some(topic => topic.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Apply sort
    filtered.sort((a, b) => {
      switch (selectedSort) {
        case 'priority':
          const priorityOrder = { 'super-critical': 0, 'critical': 1, 'moderate': 2, 'low': 3 };
          return priorityOrder[a.priority] - priorityOrder[b.priority];
        case 'success-rate':
          return a.successRate - b.successRate;
        case 'attempts':
          return b.totalAttempts - a.totalAttempts;
        case 'recent':
          return a.lastAttemptCorrect === b.lastAttemptCorrect ? 0 : a.lastAttemptCorrect ? 1 : -1;
        default:
          return 0;
      }
    });

    setFilteredData(filtered);
  }, [repairData, selectedFilter, selectedSort, searchQuery]);

  useEffect(() => {
    if (user?.uid && certificate.id) {
      loadRepairData();
    }
  }, [user?.uid, certificate.id, loadRepairData]);

  useEffect(() => {
    filterAndSortData();
  }, [filterAndSortData]);

  // Optional auto-refresh for active games (disabled by default to prevent interruptions)
  useEffect(() => {
    if (!user?.uid || !certificate.id || !autoRefreshEnabled) return;

    const interval = setInterval(() => {
      loadRepairData();
    }, 120000); // Refresh every 2 minutes when enabled

    return () => clearInterval(interval);
  }, [user?.uid, certificate.id, loadRepairData, autoRefreshEnabled]);

  const getPriorityConfig = (priority: QuestionRepairData['priority']) => {
    switch (priority) {
      case 'super-critical':
        return {
          color: 'bg-gradient-to-r from-red-500 to-pink-500',
          bgColor: 'bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-950/30 dark:to-pink-950/30',
          borderColor: 'border-red-200/50 dark:border-red-800/50',
          textColor: 'text-red-700 dark:text-red-300',
          icon: AlertTriangle,
          label: '🚨 Super Critical',
          description: 'Never answered correctly'
        };
      case 'critical':
        return {
          color: 'bg-gradient-to-r from-orange-500 to-red-500',
          bgColor: 'bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950/30 dark:to-red-950/30',
          borderColor: 'border-orange-200/50 dark:border-orange-800/50',
          textColor: 'text-orange-700 dark:text-orange-300',
          icon: TrendingDown,
          label: '⚠️ Critical',
          description: 'Very low success rate'
        };
      case 'moderate':
        return {
          color: 'bg-gradient-to-r from-yellow-500 to-orange-500',
          bgColor: 'bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-950/30 dark:to-orange-950/30',
          borderColor: 'border-yellow-200/50 dark:border-yellow-800/50',
          textColor: 'text-yellow-700 dark:text-yellow-300',
          icon: Target,
          label: '📊 Moderate',
          description: 'Needs improvement'
        };
      case 'low':
        return {
          color: 'bg-gradient-to-r from-blue-500 to-indigo-500',
          bgColor: 'bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30',
          borderColor: 'border-blue-200/50 dark:border-blue-800/50',
          textColor: 'text-blue-700 dark:text-blue-300',
          icon: CheckCircle,
          label: '💡 Low Priority',
          description: 'Occasional mistakes'
        };
    }
  };

  const toggleQuestionExpansion = (questionId: string) => {
    setExpandedQuestions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(questionId)) {
        newSet.delete(questionId);
      } else {
        newSet.add(questionId);
      }
      return newSet;
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-red-50 dark:from-gray-900 dark:to-red-900/20 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-red-600 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">Loading repair center data...</p>
        </div>
      </div>
    );
  }

  if (repairData.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-red-50 dark:from-gray-900 dark:to-red-900/20">
        <div className="max-w-4xl mx-auto px-6 py-12">
          <div className="text-center py-20">
            <div className="bg-gradient-to-r from-green-600 to-emerald-600 p-6 rounded-full w-24 h-24 mx-auto mb-6">
              <CheckCircle className="h-12 w-12 text-white mx-auto" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              🎉 Perfect Performance!
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              You haven&apos;t answered any questions incorrectly yet. Take some exams or play games to see questions that need repair.
            </p>
            <Button className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700">
              <BookOpen className="h-4 w-4 mr-2" />
              Take Your First Exam
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const priorityCounts = {
    'super-critical': repairData.filter(q => q.priority === 'super-critical').length,
    'critical': repairData.filter(q => q.priority === 'critical').length,
    'moderate': repairData.filter(q => q.priority === 'moderate').length,
    'low': repairData.filter(q => q.priority === 'low').length,
  };

  // Conditional rendering for different views
  if (currentView === 'topic_explanation') {
    const topics = [...new Set(filteredData.flatMap(item => item.topics))];
    return (
      <TopicExplanationMode
        certificate={certificate}
        topics={topics}
        onBack={handleBackToRepairCenter}
      />
    );
  }

  if (currentView === 'exam') {
    return (
      <RepairCenterExamInterface
        certificate={certificate}
        repairQuestions={filteredData}
        gameType="multiple_choice"
        onBack={handleBackToRepairCenter}
        onComplete={(attempt) => {
          console.log('Exam completed:', attempt);
          handleBackToRepairCenter();
        }}
      />
    );
  }

  if (currentView === 'flashcards') {
    const topics = [...new Set(filteredData.flatMap(item => item.topics))];
    return (
      <FlashcardsGame
        certificate={certificate}
        topics={topics}
        onBack={handleBackToRepairCenter}
      />
    );
  }

  if (currentView === 'true_false') {
    const topics = [...new Set(filteredData.flatMap(item => item.topics))];
    return (
      <TrueFalseGame
        certificate={certificate}
        topics={topics}
        onBack={handleBackToRepairCenter}
      />
    );
  }

  if (currentView === 'matching') {
    const topics = [...new Set(filteredData.flatMap(item => item.topics))];
    return (
      <MatchingGame
        certificate={certificate}
        topics={topics}
        onBack={handleBackToRepairCenter}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-red-50 dark:from-gray-900 dark:to-red-900/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        {/* Header */}
        <div className="text-center mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-4 mb-3">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white">
              🔧 Repair Center
            </h1>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setAutoRefreshEnabled(!autoRefreshEnabled)}
                className={`flex items-center gap-2 text-sm ${autoRefreshEnabled ? 'bg-green-50 border-green-300 text-green-700' : ''}`}
              >
                <RefreshCw className={`h-4 w-4 ${autoRefreshEnabled ? 'text-green-600' : ''}`} />
                <span className="hidden sm:inline">{autoRefreshEnabled ? 'Auto-Refresh ON' : 'Auto-Refresh OFF'}</span>
                <span className="sm:hidden">{autoRefreshEnabled ? '🟢' : '⚪'}</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={loadRepairData}
                disabled={isLoading}
                className="flex items-center gap-2 text-sm"
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                <span className="hidden sm:inline">Refresh Now</span>
                <span className="sm:hidden">↻</span>
              </Button>
            </div>
          </div>
          <p className="text-sm sm:text-base lg:text-lg text-gray-600 dark:text-gray-400 px-4">
            Focus on questions you&apos;ve answered incorrectly to improve your performance
          </p>
          <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-500 mt-2 px-4">
            {autoRefreshEnabled
              ? "Auto-refresh enabled - updates every 2 minutes to show new incorrect answers"
              : "Auto-refresh disabled - click 'Refresh Now' to update manually"
            }
          </p>
        </div>

        {/* Exam Creation Section */}
        {filteredData.length > 0 && (
          <div className="mb-6 sm:mb-8">
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700 shadow-lg">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-4">
                <div>
                  <h2 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
                    <GraduationCap className="h-5 w-5 text-blue-600" />
                    Create Practice Exam
                  </h2>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Turn your repair center questions into focused practice sessions
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowExamOptions(!showExamOptions)}
                  className="flex items-center gap-2"
                >
                  <Zap className="h-4 w-4" />
                  {showExamOptions ? 'Hide Options' : 'Show Options'}
                </Button>
              </div>

              {showExamOptions && (
                <div className="space-y-4">
                  {/* Game Type Options */}
                  <div>
                    <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
                      Choose Practice Mode:
                    </h3>
                    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-3">
                      {[
                        { type: 'multiple_choice', label: 'Quiz', icon: FileText, color: 'blue' },
                        { type: 'flashcards', label: 'Flashcards', icon: BookOpen, color: 'green' },
                        { type: 'true_false', label: 'True/False', icon: CheckCircle, color: 'purple' },
                        { type: 'fill_blank', label: 'Fill Blanks', icon: Search, color: 'orange' },
                        { type: 'matching', label: 'Matching', icon: Target, color: 'pink' },
                        { type: 'concept_mapping', label: 'Concepts', icon: Brain, color: 'indigo' }
                      ].map(({ type, label, icon: Icon, color }) => (
                        <Button
                          key={type}
                          variant="outline"
                          size="sm"
                          onClick={() => handleCreateRepairExam(type)}
                          disabled={isCreatingExam}
                          className={`flex flex-col items-center gap-1 h-auto py-3 px-2 text-xs hover:bg-${color}-50 hover:border-${color}-300 dark:hover:bg-${color}-900/20`}
                        >
                          <Icon className={`h-4 w-4 text-${color}-600`} />
                          <span>{label}</span>
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Topic Explanation Mode */}
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                    <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
                      Advanced Practice:
                    </h3>
                    <Button
                      variant="outline"
                      onClick={handleCreateTopicExplanationMode}
                      disabled={isCreatingExam}
                      className="flex items-center gap-2 hover:bg-purple-50 hover:border-purple-300 dark:hover:bg-purple-900/20"
                    >
                      <MessageSquare className="h-4 w-4 text-purple-600" />
                      Topic Explanation Mode
                      <span className="text-xs text-gray-500 ml-2">
                        Explain your understanding of topics you got wrong
                      </span>
                    </Button>
                  </div>

                  {/* Stats for current filter */}
                  <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-3">
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      <span className="font-medium">{filteredData.length}</span> questions •
                      <span className="font-medium ml-1">{[...new Set(filteredData.flatMap(item => item.topics))].length}</span> topics
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Stats Overview */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-6 sm:mb-8">
          {Object.entries(priorityCounts).map(([priority, count]) => {
            const config = getPriorityConfig(priority as QuestionRepairData['priority']);
            const Icon = config.icon;
            
            return (
              <div key={priority} className={`${config.bgColor} rounded-2xl p-4 border ${config.borderColor} shadow-lg`}>
                <div className="flex items-center justify-between mb-2">
                  <div className="p-2 bg-white/60 dark:bg-gray-800/60 rounded-lg">
                    <Icon className={`h-5 w-5 ${config.textColor}`} />
                  </div>
                  <div className="text-right">
                    <div className={`text-2xl font-bold ${config.textColor}`}>
                      {count}
                    </div>
                  </div>
                </div>
                <div className={`text-xs ${config.textColor} font-medium`}>
                  {config.label.replace(/[🚨⚠️📊💡]/g, '').trim()}
                </div>
              </div>
            );
          })}
        </div>

        {/* Filters and Search */}
        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search questions or topics..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 focus:ring-2 focus:ring-red-500 focus:border-transparent"
              />
            </div>

            {/* Filters */}
            <div className="flex flex-wrap gap-2">
              {(['all', 'super-critical', 'critical', 'moderate', 'low'] as FilterType[]).map((filter) => {
                const isActive = selectedFilter === filter;
                const config = filter !== 'all' ? getPriorityConfig(filter) : null;

                return (
                  <button
                    key={filter}
                    onClick={() => setSelectedFilter(filter)}
                    className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${
                      isActive
                        ? config
                          ? `${config.color} text-white shadow-lg`
                          : 'bg-gray-600 text-white shadow-lg'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    {filter === 'all' ? 'All Questions' : config?.label}
                  </button>
                );
              })}
            </div>

            {/* Sort */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">Sort by:</span>
              <select
                value={selectedSort}
                onChange={(e) => setSelectedSort(e.target.value as SortType)}
                className="px-3 py-1.5 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-red-500 focus:border-transparent"
              >
                <option value="priority">Priority</option>
                <option value="success-rate">Success Rate</option>
                <option value="attempts">Total Attempts</option>
                <option value="recent">Recent Performance</option>
              </select>
            </div>
          </div>

          {/* Results Summary */}
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Showing <span className="font-semibold text-red-600 dark:text-red-400">{filteredData.length}</span> of{' '}
              <span className="font-semibold">{repairData.length}</span> questions that need repair
              {searchQuery && (
                <span> matching &quot;<span className="font-semibold">{searchQuery}</span>&quot;</span>
              )}
            </p>
          </div>
        </div>

        {/* Questions List */}
        <div className="space-y-4">
          {filteredData.length > 0 ? (
            filteredData.map((item, index) => {
              const config = getPriorityConfig(item.priority);
              const isExpanded = expandedQuestions.has(item.questionId);

              return (
                <div
                  key={item.questionId}
                  className={`${config.bgColor} rounded-2xl border ${config.borderColor} shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl`}
                >
                  {/* Question Header */}
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm px-3 py-1 rounded-full">
                          <span className="text-sm font-bold text-gray-700 dark:text-gray-300">
                            #{index + 1}
                          </span>
                        </div>
                        <div className={`px-3 py-1 rounded-full text-xs font-bold ${config.color} text-white`}>
                          {config.label}
                        </div>
                        {item.topics.length > 0 && (
                          <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm px-3 py-1 rounded-full">
                            <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                              {item.topics[0]}
                            </span>
                          </div>
                        )}
                      </div>

                      <button
                        onClick={() => toggleQuestionExpansion(item.questionId)}
                        className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm p-2 rounded-lg hover:bg-white/80 dark:hover:bg-gray-700/80 transition-colors"
                      >
                        {isExpanded ? (
                          <XCircle className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                        ) : (
                          <BookOpen className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                        )}
                      </button>
                    </div>

                    {/* Question Preview */}
                    <div className="mb-4">
                      <p className={`text-lg font-medium ${config.textColor} line-clamp-2`}>
                        {item.question.question}
                      </p>
                    </div>

                    {/* Stats */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                      <div className="text-center">
                        <div className={`text-xl font-bold ${config.textColor}`}>
                          {Math.round(item.successRate)}%
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">Success Rate</div>
                      </div>

                      <div className="text-center">
                        <div className={`text-xl font-bold ${config.textColor}`}>
                          {item.totalAttempts}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">Total Attempts</div>
                      </div>

                      <div className="text-center">
                        <div className={`text-xl font-bold ${config.textColor}`}>
                          {item.incorrectAttempts}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">Incorrect</div>
                      </div>

                      <div className="text-center">
                        <div className={`text-xl font-bold ${config.textColor}`}>
                          {Math.round(item.averageTimeSpent)}s
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">Avg Time</div>
                      </div>
                    </div>

                    {/* Recent Performance */}
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Recent:</span>
                      <div className="flex space-x-1">
                        {item.recentPerformance.map((correct, idx) => (
                          <div
                            key={idx}
                            className={`w-6 h-6 rounded-full flex items-center justify-center ${
                              correct
                                ? 'bg-green-500 text-white'
                                : 'bg-red-500 text-white'
                            }`}
                          >
                            {correct ? (
                              <CheckCircle className="h-3 w-3" />
                            ) : (
                              <XCircle className="h-3 w-3" />
                            )}
                          </div>
                        ))}
                      </div>
                      <span className={`text-sm font-medium ${
                        item.lastAttemptCorrect
                          ? 'text-green-600 dark:text-green-400'
                          : 'text-red-600 dark:text-red-400'
                      }`}>
                        {item.lastAttemptCorrect ? '✅ Last: Correct' : '❌ Last: Incorrect'}
                      </span>
                    </div>
                  </div>

                  {/* Expanded Question Details */}
                  {isExpanded && (
                    <div className="border-t border-white/20 dark:border-gray-700/50 bg-white/30 dark:bg-gray-800/30 p-6">
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Full Question:</h4>
                          <p className="text-gray-800 dark:text-gray-200 leading-relaxed">
                            {item.question.question}
                          </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {['A', 'B', 'C', 'D'].map((choice) => {
                            const choiceText = item.question[`choice${choice}` as keyof Question] as string;
                            const isCorrect = item.question.correctAnswer === choice;

                            return (
                              <div
                                key={choice}
                                className={`p-3 rounded-lg border-2 ${
                                  isCorrect
                                    ? 'bg-green-100 dark:bg-green-900/20 border-green-300 dark:border-green-600 text-green-800 dark:text-green-300'
                                    : 'bg-white/60 dark:bg-gray-700/60 border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300'
                                }`}
                              >
                                <span className="font-bold">{choice})</span> {choiceText}
                                {isCorrect && (
                                  <CheckCircle className="inline-block ml-2 h-4 w-4 text-green-600" />
                                )}
                              </div>
                            );
                          })}
                        </div>

                        {item.question.explanation && (
                          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border-l-4 border-blue-400">
                            <h5 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">Explanation:</h5>
                            <p className="text-blue-700 dark:text-blue-300">
                              {item.question.explanation}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              );
            })
          ) : (
            <div className="text-center py-12">
              <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded-full w-fit mx-auto mb-4">
                <Search className="h-8 w-8 text-gray-400" />
              </div>
              <p className="text-gray-500 dark:text-gray-400 text-lg mb-2">No questions found</p>
              <p className="text-gray-400 dark:text-gray-500 text-sm">
                Try adjusting your filters or search query
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
