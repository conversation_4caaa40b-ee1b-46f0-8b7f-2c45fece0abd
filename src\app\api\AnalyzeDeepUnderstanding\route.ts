import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

// Main GDPR Topics for assessment
const GDPR_MAIN_TOPICS = [
  "Lawful Basis for Processing",
  "Data Subject Rights", 
  "Consent Requirements",
  "Data Protection Impact Assessment (DPIA)",
  "Data Breach Notification",
  "Data Protection Officer (DPO)",
  "International Data Transfers",
  "Privacy by Design and Default",
  "Record Keeping Obligations",
  "Supervisory Authority Powers",
  "Penalties and Enforcement",
  "Special Categories of Data",
  "Children's Data Protection",
  "Data Retention and Erasure",
  "Transparency and Information Requirements"
];

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      question,
      choiceA,
      choiceB, 
      choiceC,
      choiceD,
      correctAnswer,
      userAnswer,
      userExplanation,
      isFollowUp = false,
      followUpType = null, // 'why_correct', 'why_incorrect', 'scenario_application'
      originalQuestion = null,
      certificateName = 'CIPP/E'
    } = body;

    if (!question || !userAnswer || !userExplanation) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro" });

    let prompt = '';
    
    if (!isFollowUp) {
      // Initial question analysis
      const isCorrect = userAnswer === correctAnswer;
      
      prompt = `
You are an expert ${certificateName} instructor analyzing a student's answer and explanation.

QUESTION: ${question}
A) ${choiceA}
B) ${choiceB}
C) ${choiceC}
D) ${choiceD}

CORRECT ANSWER: ${correctAnswer}
STUDENT'S ANSWER: ${userAnswer}
STUDENT'S EXPLANATION: ${userExplanation}

MAIN GDPR TOPICS: ${GDPR_MAIN_TOPICS.join(', ')}

ANALYSIS TASK:
1. Analyze the student's understanding based on their explanation
2. Identify which main GDPR topics this question tests
3. Rate their understanding (1-10 scale)
4. Generate a follow-up question to test deeper understanding

${isCorrect 
  ? 'The student chose the CORRECT answer. Now test if they truly understand WHY it\'s correct and can apply the concept.'
  : 'The student chose an INCORRECT answer. Focus on their misconceptions and knowledge gaps.'
}

Return JSON:
{
  "understandingScore": number, // 1-10 scale
  "isAnswerCorrect": ${isCorrect},
  "mainTopicsAssessed": ["topic1", "topic2"], // From the GDPR_MAIN_TOPICS list
  "feedback": "Detailed analysis of their understanding",
  "knowledgeGaps": ["gap1", "gap2"],
  "followUpQuestion": {
    "type": "${isCorrect ? 'why_correct' : 'why_incorrect'}",
    "question": "Follow-up question to test deeper understanding",
    "expectedPoints": ["point1", "point2", "point3"],
    "minimizedTopicHint": "Very brief topic hint (max 3 words)"
  },
  "shouldProceedToNext": ${isCorrect ? 'true' : 'false'} // Only if score >= 5 and answer correct
}

Make the follow-up question test understanding, not memorization. Examples:
- "Why is option X incorrect in this scenario?"
- "How would this apply in a different context?"
- "What would happen if condition Y changed?"`;

    } else {
      // Follow-up question analysis
      prompt = `
You are analyzing a student's response to a follow-up question about ${certificateName}.

ORIGINAL QUESTION: ${originalQuestion?.question || 'Not provided'}
FOLLOW-UP TYPE: ${followUpType}
STUDENT'S FOLLOW-UP EXPLANATION: ${userExplanation}

MAIN GDPR TOPICS: ${GDPR_MAIN_TOPICS.join(', ')}

Analyze their deeper understanding and provide final assessment.

Return JSON:
{
  "followUpScore": number, // 1-10 scale for follow-up understanding
  "overallUnderstanding": number, // 1-10 overall comprehension
  "feedback": "Analysis of their deeper understanding",
  "conceptsMastered": ["concept1", "concept2"],
  "areasForImprovement": ["area1", "area2"],
  "mainTopicsAssessed": ["topic1", "topic2"],
  "readyForNextQuestion": boolean, // true if overall >= 5
  "studyRecommendations": ["recommendation1", "recommendation2"]
}`;
    }

    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();

    try {
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const analysisData = JSON.parse(jsonMatch[0]);
      
      return NextResponse.json({
        success: true,
        data: {
          ...analysisData,
          timestamp: new Date().toISOString(),
          certificateName,
          isFollowUp
        }
      });

    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      
      // Fallback response
      const isCorrect = userAnswer === correctAnswer;
      return NextResponse.json({
        success: true,
        data: {
          understandingScore: isCorrect ? 6 : 3,
          isAnswerCorrect: isCorrect,
          mainTopicsAssessed: ["Data Protection Principles"],
          feedback: "Unable to analyze properly. Please try again.",
          knowledgeGaps: ["Review the key concepts"],
          followUpQuestion: isFollowUp ? null : {
            type: isCorrect ? 'why_correct' : 'why_incorrect',
            question: "Can you explain the key principle behind this answer?",
            expectedPoints: ["Key principle", "Legal requirement", "Practical application"],
            minimizedTopicHint: "GDPR Basics"
          },
          shouldProceedToNext: false,
          timestamp: new Date().toISOString(),
          certificateName,
          isFollowUp
        }
      });
    }

  } catch (error) {
    console.error('Error in AnalyzeDeepUnderstanding API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to analyze understanding',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
