import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      topics, 
      certificateName,
      difficulty = 'medium',
      questionCount = 5,
      focusAreas = []
    } = body;

    if (!topics || !Array.isArray(topics) || topics.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Topics array is required' },
        { status: 400 }
      );
    }

    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro" });

    const prompt = `
You are an expert ${certificateName || 'certification'} instructor creating conceptual questions to test understanding of specific topics.

TOPICS TO FOCUS ON: ${topics.join(', ')}
DIFFICULTY LEVEL: ${difficulty}
NUMBER OF QUESTIONS: ${questionCount}
${focusAreas.length > 0 ? `FOCUS AREAS: ${focusAreas.join(', ')}` : ''}

Create ${questionCount} conceptual questions that test deep understanding of these topics. These are NOT multiple choice questions - they are open-ended questions that require explanation and reasoning.

For each question, provide:
1. A thought-provoking question that requires understanding of the underlying concepts
2. Key points that should be covered in a good answer
3. Common misconceptions to watch for
4. The difficulty level of the question

Return your response as a JSON object with the following structure:

{
  "questions": [
    {
      "id": "unique_id",
      "question": "The conceptual question text",
      "topic": "primary topic this question covers",
      "difficulty": "easy" | "medium" | "hard",
      "keyPoints": ["point 1", "point 2", "point 3"],
      "commonMisconceptions": ["misconception 1", "misconception 2"],
      "evaluationCriteria": ["criteria 1", "criteria 2"],
      "sampleGoodAnswer": "Example of what a good answer might include",
      "relatedConcepts": ["concept 1", "concept 2"],
      "timeEstimate": "estimated time to answer in minutes"
    }
  ],
  "learningObjectives": ["objective 1", "objective 2"],
  "studyTips": ["tip 1", "tip 2"],
  "additionalResources": ["resource 1", "resource 2"]
}

Focus on:
1. Testing conceptual understanding rather than memorization
2. Questions that reveal gaps in knowledge
3. Scenarios that require applying knowledge to new situations
4. Questions that help identify and correct misconceptions
5. Progressive difficulty that builds understanding

Make the questions engaging and relevant to real-world applications of ${certificateName || 'the certification'} knowledge.
`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    // Try to parse the JSON response
    let questionsData;
    try {
      // Extract JSON from the response (in case there's extra text)
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        questionsData = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('No JSON found in response');
      }
    } catch (parseError) {
      console.error('Error parsing Gemini response:', parseError);
      console.log('Raw response:', text);
      
      // Fallback response if parsing fails
      questionsData = {
        questions: [
          {
            id: "fallback_1",
            question: `Explain the key concepts and principles related to ${topics[0]} in the context of ${certificateName}.`,
            topic: topics[0],
            difficulty: difficulty,
            keyPoints: [
              "Understanding of basic principles",
              "Ability to explain concepts clearly",
              "Recognition of practical applications"
            ],
            commonMisconceptions: [
              "Confusing related but different concepts",
              "Oversimplifying complex relationships"
            ],
            evaluationCriteria: [
              "Accuracy of explanation",
              "Completeness of answer",
              "Use of appropriate terminology"
            ],
            sampleGoodAnswer: "A comprehensive answer should cover the fundamental principles, provide examples, and demonstrate understanding of how the concepts apply in practice.",
            relatedConcepts: topics.slice(1, 3),
            timeEstimate: "5-10 minutes"
          }
        ],
        learningObjectives: [
          `Understand core concepts of ${topics.join(', ')}`,
          "Apply knowledge to practical scenarios"
        ],
        studyTips: [
          "Review the fundamental principles",
          "Practice explaining concepts in your own words",
          "Look for real-world examples"
        ],
        additionalResources: [
          "Official certification study materials",
          "Practice scenarios and case studies"
        ]
      };
    }

    return NextResponse.json({
      success: true,
      data: questionsData
    });

  } catch (error) {
    console.error('Error in GenerateTopicQuestions API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate topic questions',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
