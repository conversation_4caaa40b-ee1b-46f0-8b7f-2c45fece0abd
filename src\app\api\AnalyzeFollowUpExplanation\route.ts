import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      originalQuestion,
      followUpQuestion,
      userExplanation,
      certificateName
    } = body;

    if (!originalQuestion || !followUpQuestion || !userExplanation) {
      return NextResponse.json(
        { success: false, error: 'All fields are required' },
        { status: 400 }
      );
    }

    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro" });

    const prompt = `
You are an expert ${certificateName} instructor analyzing a student's understanding. The student got the original question correct, and now you're testing their DEEP UNDERSTANDING with follow-up questions.

ORIGINAL QUESTION: ${originalQuestion.questionText}
A) ${originalQuestion.choiceA}
B) ${originalQuestion.choiceB}
C) ${originalQuestion.choiceC}
D) ${originalQuestion.choiceD}
CORRECT ANSWER: ${originalQuestion.correctAnswer}
TOPIC: ${originalQuestion.topic}

FOLLOW-UP QUESTION TYPE: ${followUpQuestion.type}
FOLLOW-UP QUESTION: ${followUpQuestion.questionText}
HINTS PROVIDED: ${followUpQuestion.hints?.join(', ') || 'Not specified'}

STUDENT'S EXPLANATION: ${userExplanation}

ANALYSIS TASK:
Evaluate whether the student truly UNDERSTANDS the concept or is just memorizing. Consider:

1. CONCEPTUAL DEPTH: Do they understand the underlying principles?
2. REASONING QUALITY: Can they explain WHY something is correct/incorrect?
3. PRACTICAL APPLICATION: Can they apply the concept beyond memorization?
4. MISCONCEPTION AWARENESS: Do they understand common pitfalls?
5. COMPLETENESS: Did they address the key points?

Rate their understanding on a scale of 1-10:
- 1-3: Memorization only, no real understanding
- 4-6: Partial understanding, some gaps
- 7-8: Good understanding with minor gaps
- 9-10: Deep, comprehensive understanding

Provide your analysis in the following JSON format:
{
  "score": number, // 1-10 scale for depth of understanding
  "understandingLevel": "memorization" | "partial" | "good" | "deep",
  "feedback": "Detailed feedback on their understanding level",
  "conceptualGaps": [
    "Gap 1 in understanding",
    "Gap 2 in understanding"
  ],
  "strengths": [
    "Strength 1 in their explanation",
    "Strength 2 in their explanation"
  ],
  "suggestions": [
    "Specific suggestion 1 for deeper understanding",
    "Specific suggestion 2 for improvement"
  ],
  "keyPointsCovered": [
    "Point 1 they covered well",
    "Point 2 they covered well"
  ],
  "keyPointsMissed": [
    "Important point 1 they missed",
    "Important point 2 they missed"
  ],
  "nextSteps": [
    "What they should study next",
    "How to deepen understanding"
  ],
  "memorizationRisk": boolean, // true if they seem to be memorizing rather than understanding
  "readyForAdvanced": boolean // true if they're ready for more advanced concepts
}

Be thorough and constructive. The goal is to ensure they truly understand, not just memorize.`;

    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();

    try {
      // Try to parse the JSON response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const analysisData = JSON.parse(jsonMatch[0]);
      
      // Validate the response structure
      if (typeof analysisData.score !== 'number' ||
          typeof analysisData.feedback !== 'string') {
        throw new Error('Invalid response structure');
      }

      return NextResponse.json({
        success: true,
        data: {
          ...analysisData,
          analyzedAt: new Date().toISOString(),
          questionType: followUpQuestion.type,
          originalQuestionId: originalQuestion.questionId
        }
      });

    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      
      // Fallback response if JSON parsing fails
      const score = userExplanation.length > 50 ? 6 : 4; // Basic scoring based on length
      
      return NextResponse.json({
        success: true,
        data: {
          score,
          understandingLevel: score >= 7 ? "good" : "partial",
          feedback: text || "Unable to analyze explanation properly. Please provide more detail in your explanation.",
          conceptualGaps: [],
          strengths: ["Provided an explanation"],
          suggestions: [
            "Provide more detailed explanations",
            "Focus on the underlying principles",
            "Include specific examples"
          ],
          keyPointsCovered: [],
          keyPointsMissed: followUpQuestion.hints || [],
          nextSteps: ["Review the core concepts", "Practice explaining in your own words"],
          memorizationRisk: score < 5,
          readyForAdvanced: score >= 8,
          analyzedAt: new Date().toISOString(),
          questionType: followUpQuestion.type,
          originalQuestionId: originalQuestion.questionId
        }
      });
    }

  } catch (error) {
    console.error('Error in AnalyzeFollowUpExplanation API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to analyze follow-up explanation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
