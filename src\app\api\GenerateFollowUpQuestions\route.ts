import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      question,
      userAnswer,
      certificateName
    } = body;

    if (!question || !userAnswer) {
      return NextResponse.json(
        { success: false, error: 'Question and user answer are required' },
        { status: 400 }
      );
    }

    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro" });

    const prompt = `
You are an expert ${certificateName} instructor. A student just answered this question CORRECTLY:

QUESTION: ${question.questionText}
A) ${question.choiceA}
B) ${question.choiceB}
C) ${question.choiceC}
D) ${question.choiceD}

CORRECT ANSWER: ${question.correctAnswer}
STUDENT'S ANSWER: ${userAnswer}
TOPIC: ${question.topic}

Since they got it right, I want to test their DEEP UNDERSTANDING to ensure they truly comprehend the concept and aren't just memorizing. Generate 3-4 follow-up questions that will reveal whether they really understand:

1. WHY their answer is correct (test conceptual understanding)
2. WHY specific wrong answers are incorrect (test ability to identify misconceptions)
3. HOW this concept applies in different scenarios (test practical understanding)
4. WHAT the underlying principles are (test foundational knowledge)

Each follow-up question should:
- Test deep understanding, not memorization
- Require explanation of reasoning
- Help identify if they truly grasp the concept
- Be specific to this exact question and topic

Return as JSON:
{
  "followUpQuestions": [
    {
      "id": "why_correct",
      "type": "why_correct",
      "questionText": "Explain WHY answer ${question.correctAnswer} is correct. What specific principles or concepts make this the right choice?",
      "expectedPoints": [
        "Key concept 1",
        "Key concept 2", 
        "Supporting reasoning"
      ],
      "relatedChoice": "${question.correctAnswer}"
    },
    {
      "id": "why_incorrect_a",
      "type": "why_incorrect", 
      "questionText": "Explain WHY answer A is incorrect. What makes this choice wrong?",
      "expectedPoints": [
        "Specific reason why A is wrong",
        "Common misconception this represents",
        "How it differs from the correct answer"
      ],
      "relatedChoice": "A"
    },
    {
      "id": "concept_application",
      "type": "application",
      "questionText": "How would you apply this same concept in a different scenario? Give a practical example.",
      "expectedPoints": [
        "Practical application",
        "Real-world example",
        "Connection to broader principles"
      ]
    },
    {
      "id": "underlying_principle",
      "type": "concept_explanation",
      "questionText": "What is the underlying principle or rule that governs this type of question?",
      "expectedPoints": [
        "Core principle",
        "When it applies",
        "How to recognize similar situations"
      ]
    }
  ]
}

Make the questions specific to the actual question content and topic. Avoid generic questions.`;

    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();

    try {
      // Try to parse the JSON response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const followUpData = JSON.parse(jsonMatch[0]);
      
      return NextResponse.json({
        success: true,
        data: followUpData
      });

    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      
      // Fallback response if JSON parsing fails
      return NextResponse.json({
        success: true,
        data: {
          followUpQuestions: [
            {
              id: "why_correct",
              type: "why_correct",
              questionText: `Explain WHY answer ${question.correctAnswer} is correct for this question.`,
              expectedPoints: [
                "Key concept explanation",
                "Supporting reasoning", 
                "Practical application"
              ],
              relatedChoice: question.correctAnswer
            },
            {
              id: "why_incorrect",
              type: "why_incorrect",
              questionText: "Explain WHY the other answer choices are incorrect.",
              expectedPoints: [
                "Identify misconceptions",
                "Explain why each is wrong",
                "Common pitfalls"
              ]
            },
            {
              id: "application",
              type: "application", 
              questionText: "How would you apply this concept in a real-world scenario?",
              expectedPoints: [
                "Practical application",
                "Real-world example",
                "Connection to broader principles"
              ]
            }
          ]
        }
      });
    }

  } catch (error) {
    console.error('Error in GenerateFollowUpQuestions API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate follow-up questions',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
