import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      topics, 
      certificateName,
      gameType,
      difficulty = 'medium',
      itemCount = 10
    } = body;

    if (!topics || !Array.isArray(topics) || topics.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Topics array is required' },
        { status: 400 }
      );
    }

    if (!gameType) {
      return NextResponse.json(
        { success: false, error: 'Game type is required' },
        { status: 400 }
      );
    }

    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro" });

    let prompt = '';
    
    switch (gameType) {
      case 'flashcards':
        prompt = `
Create ${itemCount} flashcards for ${certificateName} focusing on: ${topics.join(', ')}

Each flashcard should have:
- A clear, concise question or term on the front
- A comprehensive answer or definition on the back
- Difficulty level: ${difficulty}

Return as JSON:
{
  "gameType": "flashcards",
  "items": [
    {
      "id": "unique_id",
      "front": "Question or term",
      "back": "Answer or definition",
      "topic": "primary topic",
      "difficulty": "${difficulty}",
      "hints": ["hint1", "hint2"],
      "relatedTerms": ["term1", "term2"]
    }
  ],
  "instructions": "How to use these flashcards effectively",
  "studyTips": ["tip1", "tip2"]
}`;
        break;

      case 'matching':
        prompt = `
Create a matching game with ${itemCount} pairs for ${certificateName} focusing on: ${topics.join(', ')}

Create pairs that test understanding of relationships between concepts, terms and definitions, causes and effects, etc.

Return as JSON:
{
  "gameType": "matching",
  "items": [
    {
      "id": "unique_id",
      "left": "Term, concept, or question",
      "right": "Definition, answer, or related concept",
      "topic": "primary topic",
      "difficulty": "${difficulty}",
      "explanation": "Why these match"
    }
  ],
  "instructions": "Match the items from the left column with the correct items from the right column",
  "studyTips": ["tip1", "tip2"]
}`;
        break;

      case 'fill_blanks':
        prompt = `
Create ${itemCount} fill-in-the-blank exercises for ${certificateName} focusing on: ${topics.join(', ')}

Each should test key concepts with strategic blanks.

Return as JSON:
{
  "gameType": "fill_blanks",
  "items": [
    {
      "id": "unique_id",
      "sentence": "Text with _____ representing blanks",
      "answers": ["correct answer for each blank"],
      "topic": "primary topic",
      "difficulty": "${difficulty}",
      "hints": ["hint for each blank"],
      "explanation": "Why this is important"
    }
  ],
  "instructions": "Fill in the blanks with the correct terms or concepts",
  "studyTips": ["tip1", "tip2"]
}`;
        break;

      case 'true_false':
        prompt = `
Create ${itemCount} true/false statements for ${certificateName} focusing on: ${topics.join(', ')}

Mix true and false statements that test understanding of key concepts.

Return as JSON:
{
  "gameType": "true_false",
  "items": [
    {
      "id": "unique_id",
      "statement": "Statement to evaluate",
      "answer": true or false,
      "topic": "primary topic",
      "difficulty": "${difficulty}",
      "explanation": "Why this is true or false",
      "commonMisconception": "What people often get wrong"
    }
  ],
  "instructions": "Determine whether each statement is true or false",
  "studyTips": ["tip1", "tip2"]
}`;
        break;

      case 'scenario_analysis':
        prompt = `
Create ${itemCount} scenario-based questions for ${certificateName} focusing on: ${topics.join(', ')}

Each scenario should present a realistic situation requiring application of knowledge.

Return as JSON:
{
  "gameType": "scenario_analysis",
  "items": [
    {
      "id": "unique_id",
      "scenario": "Realistic scenario description",
      "question": "What should be done in this situation?",
      "correctApproach": "The correct approach or solution",
      "topic": "primary topic",
      "difficulty": "${difficulty}",
      "keyConsiderations": ["factor1", "factor2"],
      "commonMistakes": ["mistake1", "mistake2"]
    }
  ],
  "instructions": "Analyze each scenario and determine the best approach",
  "studyTips": ["tip1", "tip2"]
}`;
        break;

      case 'concept_mapping':
        prompt = `
Create ${itemCount} concept mapping exercises for ${certificateName} focusing on: ${topics.join(', ')}

Each should help students understand relationships between concepts.

Return as JSON:
{
  "gameType": "concept_mapping",
  "items": [
    {
      "id": "unique_id",
      "centralConcept": "Main concept",
      "relatedConcepts": ["concept1", "concept2", "concept3"],
      "relationships": [
        {"from": "concept1", "to": "centralConcept", "relationship": "is a type of"},
        {"from": "concept2", "to": "centralConcept", "relationship": "leads to"}
      ],
      "topic": "primary topic",
      "difficulty": "${difficulty}",
      "explanation": "How these concepts relate"
    }
  ],
  "instructions": "Map the relationships between concepts",
  "studyTips": ["tip1", "tip2"]
}`;
        break;

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid game type' },
          { status: 400 }
        );
    }

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    // Try to parse the JSON response
    let gameData;
    try {
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        gameData = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('No JSON found in response');
      }
    } catch (parseError) {
      console.error('Error parsing Gemini response:', parseError);
      console.log('Raw response:', text);
      
      // Fallback response if parsing fails
      gameData = {
        gameType,
        items: [
          {
            id: "fallback_1",
            ...(gameType === 'flashcards' ? {
              front: `What are the key concepts of ${topics[0]}?`,
              back: `Review the fundamental principles and applications of ${topics[0]} in ${certificateName}.`,
              topic: topics[0],
              difficulty,
              hints: ["Think about the basic principles", "Consider practical applications"],
              relatedTerms: topics.slice(1, 3)
            } : gameType === 'true_false' ? {
              statement: `${topics[0]} is an important concept in ${certificateName}.`,
              answer: true,
              topic: topics[0],
              difficulty,
              explanation: "This is a fundamental concept that needs to be understood.",
              commonMisconception: "Some may underestimate its importance."
            } : {
              question: `Explain the importance of ${topics[0]} in ${certificateName}.`,
              answer: `${topics[0]} is crucial because...`,
              topic: topics[0],
              difficulty
            })
          }
        ],
        instructions: `Practice with these ${gameType} to improve your understanding.`,
        studyTips: [
          "Review each item carefully",
          "Focus on understanding rather than memorization",
          "Practice regularly for best results"
        ]
      };
    }

    return NextResponse.json({
      success: true,
      data: gameData
    });

  } catch (error) {
    console.error('Error in GenerateLearningGames API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate learning games',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
