"use client";

import React, { useState, useEffect } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import {
  createTopicExplanationAttempt,
  addTopicExplanationQuestions,
  submitTopicExplanationAnswer,
  addAIAnalysisToTopicExplanation,
  getTopicExplanationQuestions,
  TopicExplanationQuestion
} from "@/Firebase/firestore/services/RepairCenterExamService";
import { useAuth } from "@/context";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  ArrowLeft,
  ArrowRight,
  Clock,
  MessageSquare,
  Brain,
  CheckCircle,
  XCircle,
  Loader2,
  Target,
  BookOpen
} from "lucide-react";

interface TopicExplanationModeProps {
  certificate: Certificate;
  topics: string[];
  onBack: () => void;
}

interface TopicQuestion {
  topic: string;
  questionText: string;
  expectedPoints: string[];
}

export default function TopicExplanationMode({
  certificate,
  topics,
  onBack
}: TopicExplanationModeProps) {
  const [currentTopicIndex, setCurrentTopicIndex] = useState(0);
  const [userExplanation, setUserExplanation] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [attemptId, setAttemptId] = useState<string | null>(null);
  const [topicQuestions, setTopicQuestions] = useState<TopicQuestion[]>([]);
  const [submittedQuestions, setSubmittedQuestions] = useState<Set<number>>(new Set());
  const [aiAnalyses, setAiAnalyses] = useState<Map<number, any>>(new Map());
  const [startTime] = useState(Date.now());
  const [questionStartTime, setQuestionStartTime] = useState(Date.now());

  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    initializeTopicQuestions();
  }, []);

  const initializeTopicQuestions = async () => {
    if (!user?.uid || !certificate.id) return;

    try {
      // Create topic explanation attempt
      const attemptId = await createTopicExplanationAttempt(
        user.uid,
        certificate.id,
        topics
      );
      setAttemptId(attemptId);

      // Generate topic questions
      const questions: TopicQuestion[] = topics.map(topic => ({
        topic,
        questionText: `Explain your understanding of "${topic}" and why it's important in ${certificate.name}. Include key concepts, practical applications, and common misconceptions to avoid.`,
        expectedPoints: [
          `Definition and core concepts of ${topic}`,
          `Practical applications in ${certificate.name}`,
          `Common misconceptions or pitfalls`,
          `Relationship to other concepts`,
          `Real-world examples or scenarios`
        ]
      }));

      setTopicQuestions(questions);

      // Add questions to Firebase
      const topicQuestionData = questions.map((q, index) => ({
        topic: q.topic,
        questionText: q.questionText,
        expectedPoints: q.expectedPoints
      }));

      await addTopicExplanationQuestions(attemptId, topicQuestionData);

    } catch (error) {
      console.error('Error initializing topic questions:', error);
      toast({
        title: "Error",
        description: "Failed to initialize topic explanation mode.",
        variant: "destructive",
      });
    }
  };

  const handleSubmitExplanation = async () => {
    if (!attemptId || !userExplanation.trim()) return;

    try {
      setIsSubmitting(true);
      const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);

      // Submit the explanation
      await submitTopicExplanationAnswer(
        attemptId,
        `topic_${currentTopicIndex}`,
        userExplanation,
        timeSpent
      );

      // Analyze with AI
      setIsAnalyzing(true);
      const response = await fetch('/api/AnalyzeTopicExplanation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          topic: topicQuestions[currentTopicIndex].topic,
          userExplanation,
          expectedPoints: topicQuestions[currentTopicIndex].expectedPoints,
          certificateName: certificate.name
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // Store AI analysis
          await addAIAnalysisToTopicExplanation(
            attemptId,
            `topic_${currentTopicIndex}`,
            result.data
          );

          setAiAnalyses(prev => new Map(prev.set(currentTopicIndex, result.data)));
          setSubmittedQuestions(prev => new Set(prev.add(currentTopicIndex)));

          toast({
            title: "Explanation Analyzed",
            description: `Score: ${result.data.score}/10 - ${result.data.feedback.substring(0, 100)}...`,
          });
        }
      }

    } catch (error) {
      console.error('Error submitting explanation:', error);
      toast({
        title: "Error",
        description: "Failed to submit explanation.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
      setIsAnalyzing(false);
    }
  };

  const handleNextTopic = () => {
    if (currentTopicIndex < topicQuestions.length - 1) {
      setCurrentTopicIndex(currentTopicIndex + 1);
      setUserExplanation("");
      setQuestionStartTime(Date.now());
    }
  };

  const handlePreviousTopic = () => {
    if (currentTopicIndex > 0) {
      setCurrentTopicIndex(currentTopicIndex - 1);
      setUserExplanation("");
      setQuestionStartTime(Date.now());
    }
  };

  const currentQuestion = topicQuestions[currentTopicIndex];
  const currentAnalysis = aiAnalyses.get(currentTopicIndex);
  const isCurrentSubmitted = submittedQuestions.has(currentTopicIndex);

  if (!currentQuestion) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading topic questions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Button variant="ghost" onClick={onBack} className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Repair Center
          </Button>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="flex items-center gap-1">
              <Brain className="h-3 w-3" />
              Topic {currentTopicIndex + 1} of {topicQuestions.length}
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {Math.floor((Date.now() - startTime) / 60000)}m
            </Badge>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
            <span>Progress</span>
            <span>{submittedQuestions.size} / {topicQuestions.length} completed</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-purple-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(submittedQuestions.size / topicQuestions.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Current Topic Question */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-purple-600" />
              Topic: {currentQuestion.topic}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              {currentQuestion.questionText}
            </p>
            
            <div className="mb-4">
              <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">
                Consider including:
              </h4>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                {currentQuestion.expectedPoints.map((point, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <Target className="h-3 w-3 mt-1 text-purple-600 flex-shrink-0" />
                    {point}
                  </li>
                ))}
              </ul>
            </div>

            <Textarea
              value={userExplanation}
              onChange={(e) => setUserExplanation(e.target.value)}
              placeholder="Write your explanation here... Be thorough and include specific examples."
              className="min-h-[200px] mb-4"
              disabled={isCurrentSubmitted}
            />

            {!isCurrentSubmitted && (
              <Button
                onClick={handleSubmitExplanation}
                disabled={!userExplanation.trim() || isSubmitting || isAnalyzing}
                className="w-full"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : isAnalyzing ? (
                  <>
                    <Brain className="h-4 w-4 mr-2" />
                    Analyzing...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Submit Explanation
                  </>
                )}
              </Button>
            )}
          </CardContent>
        </Card>

        {/* AI Analysis Results */}
        {currentAnalysis && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-blue-600" />
                AI Analysis - Score: {currentAnalysis.score}/10
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold text-green-700 dark:text-green-400 mb-2">Feedback:</h4>
                <p className="text-sm text-gray-700 dark:text-gray-300">{currentAnalysis.feedback}</p>
              </div>

              {currentAnalysis.correctPoints?.length > 0 && (
                <div>
                  <h4 className="font-semibold text-green-700 dark:text-green-400 mb-2">What you got right:</h4>
                  <ul className="text-sm space-y-1">
                    {currentAnalysis.correctPoints.map((point: string, index: number) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckCircle className="h-3 w-3 mt-1 text-green-600 flex-shrink-0" />
                        {point}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {currentAnalysis.missingPoints?.length > 0 && (
                <div>
                  <h4 className="font-semibold text-orange-700 dark:text-orange-400 mb-2">Points to improve:</h4>
                  <ul className="text-sm space-y-1">
                    {currentAnalysis.missingPoints.map((point: string, index: number) => (
                      <li key={index} className="flex items-start gap-2">
                        <XCircle className="h-3 w-3 mt-1 text-orange-600 flex-shrink-0" />
                        {point}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {currentAnalysis.suggestions?.length > 0 && (
                <div>
                  <h4 className="font-semibold text-blue-700 dark:text-blue-400 mb-2">Suggestions:</h4>
                  <ul className="text-sm space-y-1">
                    {currentAnalysis.suggestions.map((suggestion: string, index: number) => (
                      <li key={index} className="flex items-start gap-2">
                        <BookOpen className="h-3 w-3 mt-1 text-blue-600 flex-shrink-0" />
                        {suggestion}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Navigation */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePreviousTopic}
            disabled={currentTopicIndex === 0}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous Topic
          </Button>

          {currentTopicIndex < topicQuestions.length - 1 ? (
            <Button
              onClick={handleNextTopic}
              disabled={!isCurrentSubmitted}
            >
              Next Topic
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button
              onClick={onBack}
              disabled={submittedQuestions.size < topicQuestions.length}
              className="bg-green-600 hover:bg-green-700"
            >
              Complete Session
              <CheckCircle className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
